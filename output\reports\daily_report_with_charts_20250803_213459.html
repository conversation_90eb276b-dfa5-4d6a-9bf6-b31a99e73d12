
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国股指期货日报 - 2025年08月03日</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.95;
        }
        .content-section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .chart-container {
            background: white;
            margin: 25px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
        }
        .chart-title {
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .grid-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin: 25px 0;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        .text-content {
            font-size: 1.1em;
            color: #444;
        }
        .text-content h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .text-content h3 {
            color: #555;
            margin-top: 25px;
        }
        .text-content ul {
            padding-left: 20px;
        }
        .text-content li {
            margin: 8px 0;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #333 0%, #555 100%);
            color: white;
            border-radius: 15px;
        }
        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 中国股指期货日报</h1>
        <p>2025年08月03日 | 数据期间: 一年期 | 合约: IH、IF、IM</p>
    </div>
    
    <!-- 市场概况 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>📊 市场概况</p>
<h3>🎯 主要指数表现</li>
<li><strong>IH (上证50)</strong>: 2754.40 (-0.81%)</li>
<li><strong>IF (沪深300)</strong>: 4029.60 (-0.68%)</li>
<li><strong>IM (中证1000)</strong>: 6542.80 (+0.07%)</p>
<h3>📈 波动率水平</li>
<li><strong>IH 20日历史波动率</strong>: 9.83%</li>
<li><strong>IF 20日历史波动率</strong>: 11.63%</li>
<li><strong>IM 20日历史波动率</strong>: 13.55%</p>
<h3>🔍 市场特征</li>
<li>从一年期数据来看，三大股指期货呈现不同的波动特征</li>
<li>IH作为大盘蓝筹代表，波动相对较小</li>
<li>IM作为小盘成长代表，波动幅度较大</li>
<li>IF介于两者之间，反映市场整体情况</p>
<h3>📊 技术分析要点
1. **趋势分析</strong>: 基于一年期价格走势，识别主要趋势方向
2. **波动率分析</strong>: IV-HV价差反映市场情绪和期权定价效率
3. **期权流向</strong>: PCR指标显示投资者看涨看跌倾向
4. **期限结构</strong>: 不同到期日期权的隐含波动率分布
</p>
        </div>
    </div>
    
    <!-- 核心指标表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 波动率核心指标表</div>
        <div>                            <div id="7c963c59-652a-41a4-a200-490563176ffa" class="plotly-graph-div" style="height:300px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("7c963c59-652a-41a4-a200-490563176ffa")) {                    Plotly.newPlot(                        "7c963c59-652a-41a4-a200-490563176ffa",                        [{"cells":{"align":"center","fill":{"color":"white"},"font":{"size":11},"values":[["IH","IF","IM"],["2754.40","4029.60","6542.80"],["9.83%","11.63%","13.55%"],["9.97%","11.42%","17.51%"],["4.33%","-2.62%","-0.23%"],["1.47","1.38","1.03"],["31.34%","32.39%","26.47%"]]},"header":{"align":"center","fill":{"color":"lightblue"},"font":{"color":"black","size":12},"values":["合约","当前价格","HV20","HV60","IV-HV价差","PCR","隐含波动率"]},"type":"table"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"波动率核心指标表","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":300,"margin":{"b":20,"l":20,"r":20,"t":50},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率核心指标表"}},                        {"responsive": true}                    )                };            </script>        </div>
    </div>
    
    <!-- 价格走势和PCR -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">📊 股指期货价格走势图</div>
            <div>                            <div id="7960b055-cb20-48f4-a380-e1d236e734b3" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("7960b055-cb20-48f4-a380-e1d236e734b3")) {                    Plotly.newPlot(                        "7960b055-cb20-48f4-a380-e1d236e734b3",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAA2okAzMzMzMxGiQAAAAAAAEKJAMzMzMzMfokAzMzMzMxeiQDMzMzMzG6JAzczMzMwYokDNzMzMzP6hQDMzMzMzO6JAAAAAAABMokBmZmZmZmKiQGZmZmZmTKJAMzMzMzM9okAAAAAAADyiQDMzMzMzYaJAMzMzMzNbokAAAAAAAFyiQAAAAAAANKJAMzMzMzMVokAAAAAAAEKiQDMzMzMz96FAAAAAAADioUDNzMzMzLqhQJqZmZmZwaFAzczMzMyqoUAAAAAAAHKhQJqZmZmZcaFAZmZmZmZYoUBmZmZmZkahQM3MzMzMOKFAzczMzMxUoUDNzMzMzG6hQAAAAAAAaKFAAAAAAAB+oUAAAAAAAHCiQM3MzMzMoKJAmpmZmZm1o0AzMzMzM7ukQM3MzMzMVKZAmpmZmZlBpkAAAAAAAL6kQGZmZmZmfKVAAAAAAAACpUAzMzMzMz2lQAAAAAAAuqRAmpmZmZm1pEAAAAAAAF6kQGZmZmZmEKVAmpmZmZkFpUAAAAAAACSlQDMzMzMzPaVAAAAAAAAGpUDNzMzMzAilQM3MzMzMFKVAMzMzMzPvpEBmZmZmZqqkQAAAAAAAqqRAzczMzMzSpEBmZmZmZgSlQGZmZmZmcqVAMzMzMzNBpUCamZmZmRWmQM3MzMzMuKVAAAAAAACSpUAAAAAAADilQAAAAAAAXqVAMzMzMzMxpUAAAAAAAOikQGZmZmZm+qRAzczMzMzkpEAzMzMzM\u002fGkQDMzMzMz\u002f6RAMzMzMzNXpEDNzMzMzDCkQAAAAAAASqRAZmZmZmaIpECamZmZmWukQAAAAAAAgqRAzczMzMyepEDNzMzMzLSkQDMzMzMzqaRAAAAAAACWpECamZmZmc+kQDMzMzMz16RAzczMzMwIpUAAAAAAAO6kQM3MzMzMLKVAAAAAAACcpECamZmZmZekQAAAAAAAtqRAMzMzMzPbpEAAAAAAAMikQM3MzMzMtKRAZmZmZmbepEDNzMzMzBylQM3MzMzMLKVAZmZmZmYmpUAAAAAAAB6lQJqZmZmZRaVAAAAAAADspEDNzMzMzHCkQDMzMzMzO6RAMzMzMzMjpEAAAAAAAECkQAAAAAAASqRAAAAAAAAopEAAAAAAAAKkQGZmZmZm+qNAAAAAAABOpEAzMzMzM0OkQM3MzMzMJqRAmpmZmZkzpEAAAAAAAEKkQGZmZmZmOqRAzczMzMzyo0AAAAAAABSkQAAAAAAAPqRAmpmZmZkTpEAAAAAAAP6jQJqZmZmZL6RAAAAAAABypEAAAAAAAISkQJqZmZmZcaRAmpmZmZmppECamZmZmbGkQM3MzMzM2KRAzczMzMzWpEAAAAAAAL6kQAAAAAAA1qRAAAAAAAC6pECamZmZmQOlQAAAAAAA5qRAMzMzMzOtpEDNzMzMzNikQAAAAAAA+qRAZmZmZmaspEAAAAAAAIikQAAAAAAAeKRAAAAAAACapEAAAAAAAPqkQDMzMzMz86RAMzMzMzPRpEDNzMzMzOakQAAAAAAA3KRAmpmZmZnPpEAAAAAAAHalQGZmZmZmYqVAAAAAAABopUAAAAAAAHylQGZmZmZmQqVAmpmZmZnfpEAAAAAAAAKlQAAAAAAABKVAAAAAAADopEAzMzMzMwWlQDMzMzMz66RAAAAAAADUpECamZmZmcukQGZmZmZmwKRAAAAAAADEpEBmZmZmZhKjQGZmZmZmoqNAZmZmZmYIpEBmZmZmZiykQDMzMzMzR6RAMzMzMzNdpECamZmZmWGkQAAAAAAAeKRAMzMzMzOTpEDNzMzMzJKkQM3MzMzMjqRAzczMzMyapEDNzMzMzIakQJqZmZmZl6RAAAAAAACIpEAAAAAAAIikQGZmZmZmeqRAZmZmZmZipEAzMzMzM4ukQM3MzMzMrKRAmpmZmZnRpEBmZmZmZtSkQDMzMzMz\u002faRAZmZmZmYApUAzMzMzM2OlQAAAAAAARqVAZmZmZmYOpUCamZmZmQGlQM3MzMzMFqVAMzMzMzMxpUDNzMzMzDalQAAAAAAACqVAzczMzMz4pEAzMzMzM9mkQM3MzMzM0qRAMzMzMzPjpEBmZmZmZtakQDMzMzMz2aRAMzMzMzPjpEAAAAAAAOSkQDMzMzMz46RAMzMzMzPppEAAAAAAAMikQM3MzMzM9KRAZmZmZmb0pEBmZmZmZtKkQM3MzMzM7KRAmpmZmZnxpEDNzMzMzOqkQGZmZmZmxqRAzczMzMyWpECamZmZmbmkQAAAAAAA9KRAMzMzMzNFpUAAAAAAAC6lQAAAAAAA8KRAAAAAAAACpUCamZmZmQOlQJqZmZmZEaVAmpmZmZkbpUBmZmZmZjqlQAAAAAAAKKVAmpmZmZlPpUAAAAAAAEilQM3MzMzMaKVAMzMzMzN\u002fpUDNzMzMzHalQGZmZmZmXKVAzczMzMxUpUDNzMzMzGqlQAAAAAAAnqVAZmZmZmampUCamZmZmdmlQJqZmZmZ5aVAMzMzMzMBpkCamZmZmdmlQJqZmZmZ66VAAAAAAAD8pUAAAAAAAAimQAAAAAAAsqVAzczMzMyEpUA="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"mpmZmZkdqkBmZmZmZhCqQDMzMzMzCapAzczMzMwWqkDNzMzMzPqpQJqZmZmZ+6lAAAAAAAAEqkBmZmZmZtCpQJqZmZmZF6pAZmZmZmYOqkBmZmZmZiiqQAAAAAAA8KlAzczMzMzgqUCamZmZmcupQJqZmZmZ86lAmpmZmZnxqUBmZmZmZtCpQM3MzMzMoqlAZmZmZmaWqUDNzMzMzPSpQJqZmZmZd6lAAAAAAACGqUAAAAAAAGSpQAAAAAAAbKlAMzMzMzM1qUBmZmZmZuqoQGZmZmZm6KhAAAAAAADeqECamZmZmc2oQAAAAAAArKhAMzMzMzPHqEDNzMzMzPyoQDMzMzMz86hAmpmZmZkNqUCamZmZmT+qQDMzMzMzmapAAAAAAAAIrECamZmZmeetQJqZmZmZQLBAMzMzMzOlsEAAAAAAALyuQJqZmZmZVa9AzczMzMyArkBmZmZmZvauQGZmZmZmGK5AAAAAAADwrUDNzMzMzJCtQM3MzMzMvK5AzczMzMzMrkCamZmZmQGvQJqZmZmZHa9AAAAAAADCrkAAAAAAAOyuQGZmZmZmCK9AAAAAAADIrkAAAAAAAHCuQAAAAAAAaq5AAAAAAACCrkAAAAAAAO6uQAAAAAAAyK9AmpmZmZmRr0DNzMzMzFewQDMzMzMzELBAMzMzMzMlsECamZmZmeevQM3MzMzMELBAmpmZmZmTr0BmZmZmZg6vQDMzMzMz5a5AZmZmZmYKr0AAAAAAABivQGZmZmZmKq9AmpmZmZkprkAAAAAAAP6tQAAAAAAA+K1AzczMzMx6rkDNzMzMzECuQAAAAAAAjq5AAAAAAADMrkAzMzMzM9OuQJqZmZmZqa5AMzMzMzOdrkAAAAAAAACvQM3MzMzM9q5AzczMzMxAr0AAAAAAADSvQJqZmZmZja9AAAAAAAC+rkDNzMzMzI6uQM3MzMzMpK5AZmZmZmbIrkCamZmZmdGuQAAAAAAAwK5AzczMzMzCrkCamZmZmSmvQJqZmZmZJa9AzczMzMwsr0AAAAAAACavQM3MzMzMRq9AAAAAAACirkCamZmZmdOtQAAAAAAAhq1AAAAAAABkrUAzMzMzM5utQGZmZmZmiq1AZmZmZmZ4rUAAAAAAACCtQAAAAAAAGK1AmpmZmZnRrUAzMzMzM6WtQGZmZmZmoK1AZmZmZma+rUAAAAAAAOatQDMzMzMz761AmpmZmZmdrUAzMzMzM62tQJqZmZmZB65AAAAAAACmrUCamZmZmZmtQDMzMzMzA65AmpmZmZlzrkAAAAAAAHauQGZmZmZmVq5AzczMzMycrkAAAAAAAIyuQM3MzMzM0K5AZmZmZmbYrkBmZmZmZpyuQM3MzMzMxq5AmpmZmZmtrkAzMzMzMxuvQGZmZmZm\u002fq5AMzMzMzOxrkBmZmZmZuquQDMzMzMzC69AmpmZmZl3rkAzMzMzM12uQGZmZmZmVK5AMzMzMzNtrkBmZmZmZuSuQJqZmZmZz65AmpmZmZmbrkAAAAAAALSuQGZmZmZmpq5AmpmZmZmBrkCamZmZmVmvQAAAAAAAOK9AMzMzMzNLr0BmZmZmZlqvQDMzMzMzE69AmpmZmZlVrkBmZmZmZniuQM3MzMzMgK5AmpmZmZlnrkAzMzMzM4GuQAAAAAAAWK5AMzMzMzMlrkAzMzMzMyeuQDMzMzMzI65AAAAAAAD6rUAzMzMzM\u002fmqQJqZmZmZr6tAAAAAAAA8rEAAAAAAALKsQDMzMzMzzaxAmpmZmZndrEDNzMzMzNSsQJqZmZmZ1axAMzMzMzP9rECamZmZmfmsQGZmZmZmKq1AAAAAAAAqrUAAAAAAACytQJqZmZmZMa1AZmZmZmY2rUAzMzMzMyWtQJqZmZmZGa1AZmZmZmYIrUBmZmZmZmytQAAAAAAAkq1AAAAAAADQrUAzMzMzM8GtQAAAAAAAGq5AAAAAAAAWrkDNzMzMzIauQJqZmZmZQa5AAAAAAAAMrkBmZmZmZgauQM3MzMzMMq5AZmZmZmZSrkAzMzMzM0+uQGZmZmZmDK5AZmZmZmburUBmZmZmZsKtQAAAAAAAuq1AmpmZmZnxrUDNzMzMzNytQJqZmZmZ4a1AzczMzMwErkAAAAAAABiuQM3MzMzMHq5AmpmZmZk3rkAAAAAAAAKuQJqZmZmZTa5AMzMzMzNXrkDNzMzMzCCuQJqZmZmZO65AMzMzMzM5rkBmZmZmZkCuQM3MzMzMAK5AMzMzMzN7rUDNzMzMzKqtQM3MzMzMGK5AmpmZmZmlrkBmZmZmZoCuQDMzMzMzSa5AmpmZmZlbrkAAAAAAAFyuQGZmZmZmbK5AAAAAAACcrkAAAAAAAMCuQAAAAAAAnK5AMzMzMzPtrkCamZmZmeGuQAAAAAAACK9AzczMzMwyr0CamZmZmSOvQDMzMzMzGa9AAAAAAAAGr0CamZmZmVevQJqZmZmZk69AmpmZmZnBr0DNzMzMzA2wQDMzMzMzDbBAMzMzMzMtsEAAAAAAABSwQAAAAAAAGrBAZmZmZmYtsEBmZmZmZiiwQAAAAAAAsq9AMzMzMzN7r0A="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"mpmZmZk+skDNzMzMzHmyQAAAAAAAb7JAAAAAAAB\u002fskAAAAAAAE+yQGZmZmZmRrJAzczMzMxRskCamZmZmSGyQGZmZmZmHbJAMzMzMzP+sUDNzMzMzA6yQAAAAAAAubFAAAAAAACqsUAzMzMzM3mxQAAAAAAAe7FAMzMzMzOJsUAzMzMzM1qxQDMzMzMzYrFAZmZmZmavsUAAAAAAAA2yQJqZmZmZo7FAZmZmZmbDsUAzMzMzM7ixQDMzMzMz4bFAMzMzMzORsUBmZmZmZmmxQJqZmZmZebFAzczMzMx6sUBmZmZmZnSxQM3MzMzMQLFAAAAAAAA1sUBmZmZmZoexQJqZmZmZRrFAAAAAAABVsUAzMzMzMy2yQGZmZmZmRLJAZmZmZmZJs0AzMzMzM\u002fK0QGZmZmZmtbZAzczMzMxLuEDNzMzMzHW2QAAAAAAAILZAAAAAAAAvtUAAAAAAAO21QGZmZmZmabVAMzMzMzOXtUAAAAAAAHK1QAAAAAAAYLZAmpmZmZm+tkCamZmZmdq2QDMzMzMz6rZAAAAAAADItkBmZmZmZlu3QAAAAAAAnLdAAAAAAAA\u002ft0AAAAAAAFe3QJqZmZmZr7dAAAAAAAACt0DNzMzMzJO3QGZmZmZmdbhAMzMzMzN8uEAAAAAAAPW4QM3MzMzM7bhAZmZmZmZauUDNzMzMzAe5QGZmZmZm\u002fbhAmpmZmZlfuEAAAAAAANe3QDMzMzMzQrdAAAAAAADNt0DNzMzMzC64QDMzMzMzK7hAMzMzMzM\u002ft0DNzMzMzGi3QAAAAAAAGrdAzczMzMzGt0DNzMzMzJS3QAAAAAAADLhAzczMzMx6uEBmZmZmZnm4QM3MzMzMG7hAmpmZmZlvuECamZmZmb24QAAAAAAAeLhAMzMzMzPWuEAzMzMzMx65QM3MzMzMTblAAAAAAADXuEBmZmZmZne4QJqZmZmZ\u002f7dAzczMzMwtuEAAAAAAAEq4QDMzMzMz\u002fbdAAAAAAABjt0BmZmZmZqW3QM3MzMzMcbdAzczMzMykt0BmZmZmZrO3QM3MzMzMqbdAmpmZmZnYtkBmZmZmZkO2QGZmZmZmsbVAMzMzMzOXtUAAAAAAAN+1QGZmZmZmvbVAMzMzMzO5tUAAAAAAAFC1QM3MzMzMaLVAmpmZmZlztkBmZmZmZka2QGZmZmZmWLZAAAAAAACStkBmZmZmZri2QJqZmZmZ1bZAmpmZmZmutkAzMzMzM5C2QAAAAAAAILdAmpmZmZmetkAzMzMzM8i2QJqZmZmZZLdAZmZmZmbPt0CamZmZmf+3QDMzMzMz9bdAAAAAAABPuEAAAAAAAB+4QDMzMzMzPbhAMzMzMzNLuEAzMzMzM963QAAAAAAAYLhAZmZmZmZuuEAAAAAAAP+4QJqZmZmZ3rhAZmZmZmbTuEDNzMzMzBS5QAAAAAAAD7lAZmZmZmZWuEBmZmZmZke4QDMzMzMzh7hAAAAAAACpuEDNzMzMzEu5QM3MzMzMNLlAZmZmZmYbuUAAAAAAAFq5QM3MzMzMcblAAAAAAAAauUDNzMzMzKK5QGZmZmZmj7lAAAAAAACxuUAAAAAAAJa5QDMzMzMzhrlAZmZmZmYhuEDNzMzMzP63QJqZmZmZ47dAZmZmZmbxt0BmZmZmZv+3QGZmZmZmvrdAAAAAAACht0AzMzMzM7C3QAAAAAAA1LdAzczMzMyPt0CamZmZmTi1QJqZmZmZwbRAmpmZmZk1tUAzMzMzM8q1QDMzMzMzKLZAAAAAAAA9tkBmZmZmZjC2QAAAAAAA47VAzczMzMwVtkAAAAAAAAq2QAAAAAAAirZAmpmZmZmJtkAAAAAAALy2QGZmZmZmh7ZAmpmZmZmatkAAAAAAAGG2QJqZmZmZjbZAZmZmZmaptkAzMzMzM0G3QDMzMzMzQ7dAzczMzMyCt0AzMzMzMzm3QAAAAAAAlbdAmpmZmZlst0AAAAAAAJu3QAAAAAAAPbdAzczMzMwtt0BmZmZmZle3QGZmZmZmg7dAmpmZmZlwt0AAAAAAAD63QAAAAAAA8LZAAAAAAAAlt0AAAAAAABu3QAAAAAAAC7dAAAAAAACPt0AAAAAAAE63QAAAAAAAbrdAAAAAAACmt0CamZmZmdW3QDMzMzMz1LdAzczMzMwXuEDNzMzMzOG3QAAAAAAAALhAzczMzMwMuEAzMzMzM8S3QGZmZmZm77dAAAAAAADyt0AzMzMzM+y3QGZmZmZmordAzczMzMybtkAzMzMzM9u2QAAAAAAAa7dAmpmZmZnnt0DNzMzMzNG3QM3MzMzM3rdAmpmZmZkEuEBmZmZmZvW3QAAAAAAA5bdAzczMzMz3t0AzMzMzM+C3QJqZmZmZ37dAzczMzMxbuEAAAAAAAEa4QJqZmZmZV7hAzczMzMyvuEAzMzMzM564QGZmZmZmhbhAAAAAAACauEBmZmZmZva4QJqZmZmZDblAMzMzMzM\u002fuUBmZmZmZnO5QAAAAAAAZLlAmpmZmZnauUDNzMzMzM25QAAAAAAAyrlAAAAAAAAGukAzMzMzM8y5QAAAAAAAirlAzczMzMyOuUA="},"type":"scatter"}],                        {"height":400,"hovermode":"x unified","legend":{"x":0,"y":1},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"股指期货价格走势图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"价格"}}},                        {"responsive": true}                    )                };            </script>        </div>
        </div>
        <div class="chart-container">
            <div class="chart-title">📊 PCR走势图</div>
            <div>                            <div id="c7119a32-cc61-4c2e-9022-c937c801680f" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("c7119a32-cc61-4c2e-9022-c937c801680f")) {                    Plotly.newPlot(                        "c7119a32-cc61-4c2e-9022-c937c801680f",                        [{"hovertemplate":"\u003cb\u003eIH PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H+NCkgTGEzyP5U\u002fEJOLP\u002fE\u002fRW2oeVzt6T\u002f6nlKeIDfrP0YoxyEqPes\u002fQhMDOPJE8D9akcXQGz3vPwa+JKQB\u002f\u002fM\u002fdqWB\u002f43V8z+IBZ+s0\u002ffxPwAZ2vpY4u4\u002f3m32PD858D8CKf6gjA7qP1ZoRjRhGuc\u002f\u002fnOASxU26T9D2\u002fBy9ELqP67Net3oX+4\u002fqKMzJQbL7D\u002f9Zyl3W4\u002fqPy3UaRku7uk\u002fmECx\u002fUqD6z+z+41vQC3tPzOKW14mwe0\u002fxtnELvXi7z+cKlOhQAfxP03fxejSH+4\u002fNhgMFEdr7D+uy+2yoyPsP3TSw0m60+c\u002fFnrvbco\u002f6z+bkjDAYgnyP57gnHsxAPM\u002f4u4mcPak8z+aj3v3C83zP24PQ3zXh\u002fI\u002fDH1V6iEC8D+ZxLwDeG3pP0YF6\u002fnLQes\u002f\u002fljZYUWm6j9wozodcs\u002fsP9aFbWW0Ees\u002ffrBmx\u002f8g8D8YoenVHH\u002fuP5KGYPpblPE\u002fQOqQZJGt7z8AWi954\u002ffuP\u002fcGpTJD9fA\u002fnDQPE7cu8z\u002fz9SuiHDL1P\u002fpLlsvg3fY\u002fyMuarm3Z+D\u002fnk0tKzZ74Pzi39+sOU\u002fU\u002f6p6k\u002fGzB8T8Q2FPV31rrPxwRq9YBveg\u002f8rqSU5+e4z8e4iK1AmDlPz1VaoIRc+s\u002fwvzRvqxw7j\u002fV01VPkQPuPz6jXo4Vfu8\u002f06jgXI097j8oYZKBU\u002fztP65BCO7IRus\u002ftoWB5d0G8T8mlWCr7yDyP270LErzZ\u002fI\u002fBXcrGP4p7T+qEmVtbSPyP1iz97tTy\u002fA\u002fIp5nXDKW8D+gNjF8wFXzP2shis\u002fzqfM\u002fApjPZGV48T8qvd0bYUTtP\u002f0g8C3vje4\u002fmAzAG0ci7T8WMydHhZnvPwqBN5nBxOw\u002fTbK4P0Sk7j8CVrMuRnrpP1Lp3myJf+o\u002fw8vi3S5x7T9OP6izsqzyPxi74hfScvM\u002f1kyrXDF28j9PeGyDFmLyP14hsY6C+fI\u002f4l6yGQBp8T8BSQVvG9jyPz7XDwDto\u002fQ\u002fy2fHHtO58z8K+8+cVSDyP7LURHq+G+8\u002fT+8ASSu+6D8ysQd+203kP27PJT47ZeU\u002fnfvjXxFP5D9lrjIOdvLoP9CO3AvfT\u002fA\u002fapt81ZRb8T\u002fp6Oy71kXwP+jH6Lr6Y\u002fI\u002f0PhB5zEM8T9ztqrsIx\u002fqPz55xqqSguo\u002fa1BiuDse6D8KibA7N8rpP\u002f0bUEH1We8\u002f2kVcb7ZY8j\u002feE55OhCP1P3390dn1OPg\u002fh91+H86n9D8ihz83kA\u002f1P+VXlAX7m\u002fQ\u002ftsUt709N9D9Kvw+C4L70P8t1weJhYPU\u002f4vzJcdae8T+6DWI\u002fLiDwP8qPaw+omew\u002fhmpqjcbZ6z\u002fOu1h2yofwP6o5SBIaCfA\u002fgJMDFJ9k8T+7XbS7tFjxPxqKNzrp+Ow\u002fYHn5Iinc5T+GSXhVzAjpPysdO2jhLu0\u002fTsdP0FpH7D9zHMB\u002feyDvP\u002f2z9AHSXPI\u002fC\u002fmlcMl88j\u002fuH7PnQ6DyP+8KUcvEsfU\u002frKvMPKtp9D8BPy9z0WzzP8Fsopm1H\u002fM\u002fS1op7DCn7z9HEcmziMTmPzaUhbLRM+o\u002fo3CPSCpB6j9sswWmPdznP9OZo7MEzec\u002f+NujdkIV8D9DaoaEM53sPzAMeDHpLek\u002fRqcqnUZi7T\u002ftpRuI5xXyPwCAKpaHxu0\u002fQYMfs8\u002f28D\u002fcI22HpOzzP9P0s082t\u002fI\u002fU8tAi+B68T9Qy\u002fobXxXyP85thcNt5PE\u002fN0\u002fwKQhH8T\u002fo6q7\u002fYbXyP4spKUz\u002fSu8\u002fdbEWhvzj8T\u002ffss39LmXwP0MIXdrKf+w\u002fuhydEUi\u002f5z\u002f2MT9akI3sP7bAhxnoCes\u002fN+aS5FIe6D86BumvdD7rP8DYsgHiBu0\u002fhtsF70eM7T8GVBYrn7foP+OYT3LnMO8\u002fW5KPMdP87T9fGkb2JWfyP\u002fOhpP+w7u8\u002f+Meywi\u002fE8D+qpytzMv3rP9KqzEOkk\u002fA\u002fekQdlqlK8D\u002fQL3Koot7wP9mHgn2FZvI\u002fDBTGrknH9T9mRMJLQQH0PxBCTN0DVvI\u002fZi3CmD5C8j\u002fYld1UUhPvP8MrKO3p1+8\u002fArQy6yiU8T9A01rARxPyPwgJUyaxivI\u002fyriQYmXF8z94nVaf8\u002fLyP9aSUhTj7vI\u002ferhz1\u002fsm9D9ZP9HssUT2P9MstciDrPc\u002fOKSPBNqX9D\u002fUiVGHFxDxP9BcrfkuHvE\u002f\u002f2SxbQ1J8D+SjJzB2X7qP1bunddxqeo\u002f4PsKEdl67z+OSi4gAOfmP37pDKHXn+I\u002fNbovy87N5z8DE\u002f881XjtPxZD0pN4XO0\u002fjq9KJzJ37z+90BJzH2HyP+qvrdVe4vA\u002f6\u002fNuowpA7j9GbxTdWyjvP1rmR7\u002fdnvE\u002fgkIaH0RH8j9Dt\u002f0jz0v0P9Euz6xLdvU\u002fdnJLUR1U8j\u002fmrgfesfnwP4JvGHcZWOw\u002fWmjVN5Rf6D+2p46rLELsP7WsVE4JIu8\u002fMfmG2FAV8j+DBnBw+tbzP7FkfvPTe\u002fY\u002fW+wXQdU59D8K6DOssRv1P6Ldk8LAMPM\u002fm0xB2R8Z8T9ymr6dNcDwPyPAZgMqX+8\u002fW1uFwR8R6j+YR4l4BonrPyB+2pt2XOs\u002fgBtrazx07T8="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8FL0eLRUjnPyiCxQrubO4\u002fUFVNPBL57j+x1m3cmnHxP\u002fr24d6ebfA\u002fhkjyE0Nh8j+DozBT3ibwP\u002fOmXqvuzfA\u002fiqkqcivb7j+joP9uqh\u002fvPzjj4QwkM\u002fA\u002fKhledNq67j\u002fVRkBeC+vqPy1a5COegus\u002f5TWlOzmz6z9aR\u002fYoqHnrP9kP3YhnwPA\u002fHEIoXX148z8VVGEbn83zP+Ub3RtLBPg\u002fV+PtAuMb9j8y03ABqz\u002f1P+ReichO+\u002fA\u002fLnEhgBxw8z8i1UZLbEnwP16YCZr9z+4\u002f2ApghK5u6D\u002fejzty59rsP+Zd\u002fC4Fz+g\u002f+m+fSpkO7D9SAcAY\u002f3bxP4oEbBFCzfU\u002f16y7uyqQ9z9OhR0zMM33PwGI4+EHP\u002fU\u002f6gGdR1Jr8T\u002f2Ffs42qHxP8OxkgxUe+4\u002faMIsvCnf8D\u002fEMUPkUJnzPyfNQP2ov\u002fU\u002fItQewSt\u002f8j+hsbfZ4tL0P7bsCGQuqvE\u002fXyBtlyRh8D8tfmSQZCHyP7WYBP\u002fYVvQ\u002fbMpM6dAr8j80nXvlh97zPxEJKMzy5vM\u002f5XiTkF6f8j8lKZ9R1wHxP3RPhYtsFvI\u002fFeMehm\u002fH8j8WF0ywHLvzP\u002fgPpzFi1fI\u002ffrzodBXo8T872yuZrUrvPwcnbj0wFfA\u002fkJ+dKNgZ7T+DooL6vzbuPwbHoxCfHO8\u002frgQzbzuW7j+CLHFjY9PqPyKto4FiBOs\u002f81OmK3cO5z9iJESqc9PpP5vSa1tWy+o\u002fIA1BY0sY7j\u002fjEzHjzQPxP7qBTaYNL\u002fM\u002f\u002fb+gMqTa8D+mqOIftjT0P\u002fY7gMlaCPM\u002fJfPWFvXk8z8K7QfDooLxP3mhNDnY3vM\u002fYTVoQ5f58z\u002fy5fSbSVr2PzOxPI13JvI\u002fk\u002fefqOCB8z\u002fAr9fDhA3yP6gK3XsMc+s\u002fwcmaj45U5z+pT2QapyXnP94LotUTZeg\u002f3VQIYW\u002fz7T\u002f0YznrL+nyPyo5VYfmwfQ\u002fEay7f8xI+D9FpEXeojz2PyePy2t9JfQ\u002fIyGpUVAo9D9ASlb9C83wP7g5yCRKwe4\u002fqp\u002fS\u002fcJz8T\u002fbPq3OQbnvP\u002fKO21+Q7Os\u002fy58ctaDn6T\u002fby\u002f2G\u002f6PoP71cns0G8Ok\u002f+v+4IHDQ7z\u002frzxVA9BLyP1ODspyrlvU\u002fldeEHzcg9T9zlyM2I9z1P58Bg\u002fCNE\u002fU\u002fvgGZesic9D\u002fiJ49tsE\u002f1P9KC3Q\u002fYQfg\u002ffEc0tjTO9T96KFQmowb2P\u002fTzTE0gDPQ\u002f0wubAorz8j8SFpaGfkzxP+rCxMW1BfA\u002fNYGhfHU+8D9ajV\u002flmkzuP9onCsd5ye4\u002fJTKBAeAl6z87F+hPoPzpP25SCboPA+c\u002f7nYJjARa7j8zk+zR7e3tP6jVntOEcfA\u002fW2+fzur88D962ZfLJFjvPzILyNbWAu0\u002fc84xklv57j+Ib9rFESjrP\u002fPyorXkmu8\u002fw1xKpJQC8z9hGBCaQ+3wPxWfVIUeLu4\u002fehqWLqjs8j9ld\u002facofvyP\u002fbdX899TfE\u002ftCGH\u002fL1F8D\u002fmVgCEoxnwP6oNNrBU3+w\u002fRV5s\u002fO\u002fj6D+SwEBG9+3lP4gG9FTOEOo\u002f7NQkg76T6D9WUXi8QiTmPzIjSem\u002fTuU\u002fkOV9gK6t7D+NZp299TTrP7A7SN9O9e8\u002fEPtMj\u002fYl7j9zHIOc1kLtPybOvapjF+4\u002fnKnbLXaT8D+gDiGx72HxP1LQx+c\u002fj\u002fM\u002fnXckuQtQ9D9MLZ1jSXL0PwMjlHo5AvI\u002f1YDD9yUD7j9UCcnlvjDxP1nQiaaE0fA\u002f3UUd4Fza7D9q6ZnDauPxPxDskMsfpvE\u002f9pqupabI8T\u002fxLaDb5i32P3KZGf1JVfY\u002fHifxGMqd8z\u002f6CHFid8H2PzfeaBHH7fY\u002fcPc9tUhH9D+UXPE4DSnyPxcHseWYw\u002fI\u002fFjEoJBmA8T\u002fd20fuYGzvP\u002f1ovbU0y\u002fA\u002fEuEXK4Q+9T9lirMLmUb2P1af3R1PTvM\u002fa1v2cKch8z84ABTljO3wP6pCkdM+FPA\u002f9nWj04oq7T8suEiESo7wP2Jg8XUjGvE\u002f+N7LOgvp8D9TN08GRvXvPxZJ46CpIe4\u002f\u002fCb5CyOf8D9gkA5N567tP9W7EVFWpPA\u002f+zOCU4i+8T9hAwPuq6D1PyeMUHFyO\u002fM\u002fbAtHjzcP8j+S7mP3Xg\u002f0Py7etfU\u002f0vQ\u002fUe0uedKU9D\u002fIcRCLbBf3P5anCNp6Cfk\u002fX98vZ2Gp9j+ghUvMkJj0P5In16uQsvA\u002fgg\u002fqP9vt8D\u002fZ2lGFGYPxP3rZpSBKBvQ\u002f6raNeXyq9D+U923iBdv1P8pB+H7OX\u002fI\u002fhZFsQ0pJ7z8iLS1PW+boP4JN+WmuquU\u002f8N+O5Qbd5j86fPdfOobrP4h+3xv65+s\u002fcI+UQWed6z8qSnmH7ybqPwvQvZqKz+s\u002fWt6cznQG6z9it3u6a1\u002fwP1uXnYps++8\u002fgpVPrzg+7j8dyIavQVvoP9lXOsRBM+c\u002fi77Ue3kU6T9wu2lRYSzsPyQ6axiNgPA\u002fe77rqWft8j9COL5KxTbzPy1+ql6DQfM\u002fCKRHLwuj8j+ap4iPWAz0P3EITpw6bvU\u002fZsPMbMjN9T9wO1mScoDyPwLphzUYQfQ\u002f7q4ce71Y8D8="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H\u002fEAVhA8lbwP9MTeuJyg+4\u002f0DE6xtOk8D+A6qN\u002f9hDuP3C3UdchgO8\u002f6uY0vyxv8T+OCAhZ1h7zP57ds5cObPM\u002fSq1AA+OC8z+Kf5stvpvxPxvHYPy7fe4\u002ftsgZejp27j9acJwiNZHxP+jgMohLv\u002fM\u002fkoZRHi5O9j8tgeWHLEn2P8MPEOQrv\u002fQ\u002f2n8aCvfi8D9KVCncEbbtPy4ERK0O5e8\u002f1poPTl0p8D+iBbxYy0vwP7j6FZrdxPM\u002fVDuBVB1I8j84XVh8EMrwP\u002f2iYPcMUPA\u002fPvFE3xHn6z\u002fP\u002fJWym57kP9dEK+OJI+U\u002fFE3LvbQa5D+CV0g4UDTqP5o0D+85B\u002fE\u002fIhWRp5tk9D9Gp+6gATv1P8M8T2xohfU\u002fwRz3w2j98T8SGPJCrqXwP2NXEXclQ+o\u002fNmSgarcc7z+PN33IzJDwP1unniB3SPQ\u002fyIJtBWdH9D9GbnqETkf3PxVLyfapgfc\u002fDtw3MOJA+D\u002fkVbxyi7X0P8hFq4zBffM\u002ftFfMeTaC8j9eSNUQayLtP7b9EOVvJeY\u002fagR4qAKv7j8OoVTSgujsPwsAVpU3ZfA\u002fPyJCPB+p8D9x8\u002fzf2o7wP2PxM0eptuo\u002fXUnY88sO6z8C2Uf0Mn\u002fmP1jYXF8IGeg\u002fKnDn2tlH7D+laaJIGCbqP4p1ysGu2+4\u002fM5gSkbBi7T+HuLspkHrxPzZWHTuTFPM\u002fuf9ms\u002fmy8j\u002f9\u002fEnnbPfyP03xFT586\u002fQ\u002fpvOxGNe58D\u002fSl\u002f4a08LrPx99pKlGg\u002fA\u002f2kgdOYyI7j8l0t9UWJXuP3DhGdKAJfI\u002fLW2igVgr9T+eCu68xP\u002f1Py2\u002fq1sLufY\u002fkPK5bEL68z9QyvnNtE\u002fzP8Tvm9VK9\u002fI\u002fCoG1PecH7z\u002fNktG+2aLpP4WF1uYsrOk\u002fwkjk6BQg7T8TjQsGCUXlP3NDFGWR4+k\u002fiNTnGXX27D\u002fNw8fhdpzxPyUftiDUXe4\u002fcm6cxOZr8z+7PJUQ3nXyPyZrWgXmsPI\u002fNn3K99mX7j8RIFXldHzwP3KdLRE0NO4\u002flgAXeTc58T918Q23EBvuP3UT5Q94bvA\u002fPL3fTenh8D8WJ3ijKFftPzHNYnzYP+Y\u002fSIBhjUze5z8rUheWJ0XrP\u002fkH1y3RrOg\u002fsjYHW0v96T+SGxjfiQDsPzjJxMlJcOw\u002fmswoVYFI6T8V+DPWiYfmP0pJVjjp9+k\u002fMMZMjFLT7D8T8gX5ngbxPzqz4y++f+4\u002fE\u002feYTPta8j\u002fXj1DRSejyPzdQERSnxPA\u002fftU65Ohu7T8+HEcm0WDwPw6Cm3anrfA\u002fU42cuvJl7j+cBKP\u002fJSLwP1bzPqfWQu0\u002fI+w2g3TZ7z8VA\u002fuYXSXrPzDU\u002fHV0bes\u002fa\u002fjXnG9s7D\u002fRV80UmFvyPx5gqMH7WPE\u002fezpl9\u002fyD8z9CpTNxukz0P9ZmST\u002fC2PM\u002fLvMWhTuL8T9BQ4FVSYrxP5RJS\u002fryLfE\u002fw+g6UitZ7z9GlHsCt5nyPzPR6xNNaPI\u002fi6ldDaz09D+LLKEOg4r1P2EpCskyT\u002fU\u002fxlnDJwtt8z92h3smGSjzP\u002fqqSZdnZvM\u002fjPcwfT+Q8T\u002fTb2XNnKzxP+sTdJY0f\u002fE\u002fnaTmkxFj8D\u002fGeG7HF87sP2jJXd+mNe8\u002funQpozJE8j8y80g8iObzP933iz77ZPU\u002fa3xQElIk9D8CXzFWeAj0P0a+Vnzkh\u002fI\u002f+jN7YCOd8D\u002fKAUDNgn7wPxiiCkok8\u002fE\u002fIGwIyRh27j\u002feKOaDfI7uP\u002f1oTw71DvA\u002fQ5pWNK0w8D9ToRI6Pb\u002fvP95F9fg19PM\u002f6GiZmB7y8T8y5fTD+NPxP+LGGFxO4fI\u002fXA6LhufT8j+LkRgxSRPwPzitzTvd5fI\u002fL3mOyEnJ8z995y0Dc17xPxttkTZEgPM\u002fttO7kKxU9T\u002fG3sBKELfxPwfQ+X9h+vA\u002fo9Px1Dyq8z9VCFk2VB7wPxtUmGl06e8\u002f\u002fScB7pIQ8D\u002f4K\u002fIuT4XrP4Lln5gvlOs\u002f0YKLgs9U8T\u002fN3jUvjynwP1Ff+3JJbfE\u002fLltyh6F\u002f8j9VlvZSL5\u002fvP5xDsEaHtOY\u002fdVHagQ4g6z96TypSEAfpPwqBMAz9B+s\u002fk\u002fqAn7rw7D8un7wTc\u002fnwP6NbCYfmxe4\u002fHb42Vh9w7z+N2CCTCjTvP6t7pgD6iew\u002fmprdA5i56T\u002fAV+HBkkjlP2Y86v8gE+w\u002fZFVITuj46D+yjPOxK3zvPxKDb2kHW\u002fE\u002fF8v\u002fOddw8j8VaYVBtJrxPxKOmelp7fA\u002fqru2oWOM7T\u002fDJNwCx\u002fbqP\u002fNGlaC2muk\u002fOcP7klzp5D+Kf+8p1TvrPyvpYEaIG\u002fA\u002fBjvbPkdR8j97qNOwI+7xPz2RlS2Yi\u002fM\u002fZ\u002fm57U1b8z+SnUyR+YrzPyAaKYKAq\u002fI\u002f7ZLrAl5I9j+A93LJmJD0Pysecf4yJvQ\u002fV64uWmQP8j8CC5zyKAPxPwcyG2yAGfA\u002ffOxpzMv98D9mD+Pg3+rwP6A+qheQBPE\u002fCmZ+Fx3L8z8GNei3tNXwP4oNFsTomfI\u002f2coQtSB58T+SuIF\u002fpInyP6aLmlq9WO8\u002fC8tI4Gj88j\u002ftiLiQS6\u002fxP5GN11gAtPE\u002fcr\u002fj4Ow\u002f8D8="},"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"PCR=1.0 (看涨看跌平衡)","x":1,"xanchor":"right","xref":"x domain","y":1.0,"yanchor":"bottom","yref":"y"},{"showarrow":false,"text":"PCR=0.7 (偏乐观)","x":1,"xanchor":"right","xref":"x domain","y":0.7,"yanchor":"bottom","yref":"y"},{"showarrow":false,"text":"PCR=1.3 (偏悲观)","x":1,"xanchor":"right","xref":"x domain","y":1.3,"yanchor":"bottom","yref":"y"}],"height":400,"hovermode":"x unified","shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":1.0,"y1":1.0,"yref":"y"},{"line":{"color":"green","dash":"dot"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":0.7,"y1":0.7,"yref":"y"},{"line":{"color":"red","dash":"dot"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":1.3,"y1":1.3,"yref":"y"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"PCR (Put\u002fCall Ratio) 走势图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"PCR比值"}}},                        {"responsive": true}                    )                };            </script>        </div>
        </div>
    </div>
    
    <!-- 波动率分析文字 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>🌊 波动率深度分析</p>
<h3>📈 隐含波动率 vs 历史波动率
通过IV-HV对比分析，我们可以观察到：</li>
<li><strong>期权定价效率</strong>: IV高于HV时，期权可能被高估</li>
<li><strong>市场情绪指标</strong>: IV-HV价差反映投资者对未来波动的预期</li>
<li><strong>交易机会识别</strong>: 价差异常时可能存在套利机会</p>
<h3>😊 波动率微笑现象
波动率微笑曲线揭示了期权市场的重要特征：</li>
<li><strong>深度虚值期权</strong>: 通常具有较高的隐含波动率</li>
<li><strong>平值期权</strong>: 隐含波动率相对较低</li>
<li><strong>微笑形状</strong>: 反映市场对极端事件的担忧</p>
<h3>🕐 期限结构分析
不同到期日的隐含波动率分布：</li>
<li><strong>短期期权</strong>: 受即期事件影响较大</li>
<li><strong>长期期权</strong>: 反映长期市场预期</li>
<li><strong>期限升水</strong>: 通常长期波动率高于短期</p>
<h3>🌊 3D波动率曲面
三维波动率曲面综合展示：</li>
<li><strong>执行价格维度</strong>: 不同行权价的波动率差异</li>
<li><strong>到期时间维度</strong>: 期限结构的完整展现</li>
<li><strong>波动率水平</strong>: 整体市场波动率环境
</p>
        </div>
    </div>
    
    <!-- IV-HV分析图表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 IV-HV走势对比图</div>
        <div>                            <div id="b4112778-cb42-475d-b8bb-6dd10c9f934c" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("b4112778-cb42-475d-b8bb-6dd10c9f934c")) {                    Plotly.newPlot(                        "b4112778-cb42-475d-b8bb-6dd10c9f934c",                        [{"line":{"color":"blue","width":2},"mode":"lines","name":"IH HV","showlegend":true,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H95Dfov6H8lQFXPOgTVCCVA7gkS1UXMJUALlAg83qYlQC6Ayar51SVAtMgdAxw2J0BRFV4\u002f6TknQKXR\u002fTiaOidAxOgtHyhnJEDS7gY7ktsjQCOKBBtMNiRApnX\u002fW9AXJUAFcvV0gBclQKSf7j26rCVAqOTw4IeGNkBCJwNY\u002fMs2QOtw6j2LhT5A5qiXCkBIQUA0gMRcIRxFQPO9SzcGQ0VAwTRozlwPSUCFTf97s21JQApwoSzs3UlACQME4YvUSUDq+7vgFmdKQMxsU3hKLEpAUVVKOmWGSkA2rADXt8VKQKH9bAcEvUpA6eNAHCGjSkDC80\u002f8+6VKQPLWbHtr5UpAVoE4caTeSkBRXgXoHeRKQEINoXDzuUlA50cfOVLzSUAl2guFOgtIQLqkFPjjREZAX\u002fkGC7c2QUDllsO3HrNBQLHZChYLiTlAAtYCy5AfOkDq5T5FZn45QO4iCuNPbjlAf2ITZ5iLOEDtcb8j55w4QETEWEH99jdAg5c8OTtSNUCzk4GXyVk1QLNem4g9STVA+SJ1Ax86NUBcbB00z\u002fI0QBOfveLuszdA9poiqWq+N0CuQRCRZso3QDS0Pd1z2TdAHNXpgxfsN0C8TRgXC8w3QIOC5dR8nzdA1WaybW9ZNkDDIf0FRy82QPdans58RTBAcen\u002frDxHMEDK43xZfzcwQAHJvZi5YS9Av8xUmEEJL0C+mIp1acsvQHe7dzdX8jFA3MZepKThMUDgKgc4V\u002f0xQO+oIXnTKDJA5krCKxUrMkBYz5\u002fw8FEsQD3\u002f0MAtAyxAAfNqodzTLEBEURabWd0rQIRDQgR+cytAnALSu5N7K0CvKqbGmqcrQMmslceJYC5AiycX8XZVMUCaa19tPaYxQJIttc8rIjFA0s6ciB5LMUDw34qMDuIwQCdfTl+U7jBAQ01V8YQ\u002fMEDd7ucF9iErQHijOU4H9S1A9jKvFF2ULUBWwrUk4hgtQOVqKrFINC1AhEl8pxBMLUDbCm+MMIgsQBrHDRBqAyxAqnIGOfaBLECgWjeKWoMtQK3t\u002ftPM0C1ALZN7mOLpLEBnu5YS63AsQEp57tAedilA+4pQAbBbKECk3rBGNj0oQLJ9C8EYCSlAYFeG1ocHKUDwwLy8DcAoQP4Ax3gt3ydAQjozenc5KEDnbz\u002fg8tElQCGYUcnVMSZA\u002faUd0QdZJ0Bao\u002fmGcvYnQBIrkhb8fClApRMOBBzoKUBvRMeEkWInQLQ4Xm8PFCpAIEjFtuE4KkCqay51E4opQFBmsbUWgylAvqMQGlCWK0DS4YScLm0qQDqT66ih5ypADBxCr7jNKkBHbJPxMOcpQF1SknSN9ylA5ycs2VjhMEDqugraG\u002fcwQAyU51B60TBAt34E8R\u002fNMEA7yw4mXy4xQDE\u002fKUr30zFA4NXp\u002fxTeMUBqHZ86FGUxQPNAPfE8SzFAu\u002fJDrLpCMUDpyn6adIAwQLP5tS1HXzBAQDnBuMdVMECxqJ2VuTkwQNEfJClrnS1A3++n\u002f0IxQECk+via4URBQMIiyOTLtkFACa0EYLzIQUBJEiLzS9JBQKKiekvU0EBAnO8e+OPSQEB6Q9UKutpAQHYdnCSw30BApzcx0NTMQEB8e6rrsYFAQHI8S2f4dkBAKNiQyMt4QECyJ5oWtndAQA3JStkEb0BAUqr8lhdrQEBcclcqlWhAQJkZUdRBbEBAwSO+P6F9QEDfzGHZbohAQKTYlhjYuilAguzNfdm6IUDfpANVgoAZQN2tlKkBnhhAzUysA0rWIECcGtUQI7chQLnT5XI4yCNAQJEO+RjgI0C0JJv6A8QjQDYDy0YJ5yNA685pc8XVI0D9ZiKosAMlQE3VKKv88SRAJ1s1rLJyJUDXmbbfz1MlQA2n3hsoYiVANa+bvf9dJUBhCrMqTPokQEpM3VMZZCRANOqWEfL3I0BxVMWz12IjQLXCYc51ZSNAaVZbsnMRI0BQzL\u002fqqhUkQNYDZpiBOB1AfqzLd6KRHUAdK6cBwl4bQBWveM4YPRtA4T+WRP6TGkAtEEfQCNIaQDPOrIRg5BxA5j4ME8w0HUBjD2YJ86sgQBnYAgEQPSNAF\u002fRUd9CLI0CT1dDnmU8lQMbWbRzWViVA8lQYa9pWJUD9VC9QNV8lQELRrqmuZCVAbDmk0sGxJUAvblJzvOUlQFy\u002fuOMMxSVAyKKTFzgrJUDUgDUqBXolQGJ2DTjl0yRAXSlxz\u002fjIJEB8orwMYzslQKyJumwzPSVACFzkgAqCJEDUS+DnhaIjQJqGVf1QZSNAlJIF5qYBI0APuc+CbVUgQEOss8j41x9AjoQZqv9XHEAW5ExC6lQcQFiBJqT9NhxAC9BNVP41HEAih\u002fu+Ec0iQKx7ziJCqiNA"},"yaxis":"y","type":"scatter"},{"line":{"color":"red","dash":"dash","width":2},"mode":"lines","name":"IH IV","showlegend":true,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H9CHFpYwlQqQClNSCHQBCpALMLxDJV1IEAIbuC+jEguQBT1utHjaChAOaZhMhJnKUCKjy8xOswlQEiGyGc1HC5A+HBwspUCIkAR6SEFRCUnQIpEKCwnzCNAGDt4i7VcJ0DgTh6+O3wfQFT4wXOtxSZAUjdU4zrtNEDXMxxGMbg1QAqCMg0K1zxAOd57zYD1QUBE+frr6zFFQOWu7OtaqkVAz6A+RpQkSkCGHgQX0wZKQDKedghpPEpAs9zgDlP5SUAeaYfJtpJKQIofeQqQ60lAgF\u002f7ejjBSkAb9LuZizpLQKpB4mWG\u002fkpAauDk5tBOS0A2Bvyre\u002fxKQH339caFwUpAtHTPV92aS0DQkYWE0x1LQPybiKAsJEpAk8NN\u002fRyBSUBy0AwVmcdHQHr5ocxOfkRA75KYenSRQUBWSAdCJCxBQKUyS605Jj9AHFRqkvmcOkC5J91zDdk5QKC4VxdXjzdA1RQGOK26OUAjKl3HdpA6QAbQhp+gfzlA2AU2269ZNEARe2uv3v00QAjZpw35CzZAr\u002fEf9g6xNEBpZHGMUEAzQFY95ZXx1DlA1npT3q2eM0Awqe1J99M4QGzpPII9AzVAo\u002fwW+qI+NUCDIwrcSBs7QMkMPJgdgzdARgQKG3JSNkCEC8J0f481QNq474\u002fG5yhAFNwPB9XpMUDwFSzEycUyQPxCpqQOCTRAWMJKGmdNLkBvtuRXwjEyQOX+7V5+Ei9ArV4TvmfvM0AA1cRSTAQxQH+5wqlenDJAH+CjnPQmMUDoPqBr1h8kQJ38Er2GwSlADAKveFsiMUBZmxuahLEoQE0lGQYOSStAa1Pj9QCJLkCf\u002fjBCXpwoQBYfrh2EdzBAozj1OxQeNEB87LkOYtkwQFfN8cfXaDFAxm5x1CisMkD367o7KNMxQL22985l0DBA1e4yn3qqL0DUEPaYgbMnQA4QaRBxETFAoZ3ECMRAKEC7J6dp5kgsQObR\u002fCcZ5jBANUSBNGSxMUCcG0wYxXgwQFAyVy9IfC9AJLfeDbbuLUAeZLMzx4YwQKJ1veFnEjBAYVyvR5iEMkCMAIGsU\u002fAsQDRM7wFBrytAuFO\u002fcvQqI0AkZ7joEDApQGP+9RRCwSNAr5c0zdJOLUDjt3YQ8M8nQHW7L6mIDSdAaFvjOQ0+JEB2bLrSG7opQAdH6kGTNCdAyL+eDUjALUD8fwa2vf0iQDGHFhKyDShA8sHN\u002f0LiJEC39Gz8rb8mQKYXUXV7ZypAriWbr8zyLUBS0BmjvEkiQGVS+1FyUS9A0APuegIjLUBmNu1ytKgsQCIXiyuT1ipAS1MRHWoYKkAECCIMa9EkQC2wEQc0SytA+TGXAv6sLEDSNnutu\u002fExQAyVcbesNjRAlktLosUkL0AzXdAnNGoxQPlCfBAY9jBA1fZQRMOpMUBNf8Etm1MwQD4NERfmEjVAIBMgbUdSLECtzzMJOdAwQOHs+z8FJzJApkv9gMm3MEA7ZRUT4xo0QN5C5OOq+zJAHFzyhNAtP0DVL50dfxtAQMQ60EgazEJAdPxmkQ6sQEBPg79MQPxBQO3wqiz6FEFA1C630xFcQUAcSRs4O3tBQHOMq5nHCj9AUq94tIQBPUAoRTLvYdk9QN2I2V9twj9A4D4\u002fA9qmQEBgqC7x2SJCQHfDLYij7UBA4JdFCTQsPkCUFtq94lI+QCKMQ72bnz5A0KAMWRYyP0DHrcUM8Pc8QJ9oPHBh4S1Aah56HjOwHEA\u002fjU1WTNsYQMLJXZsyaRtASh4bmAhPG0D8MjJ3ZgAbQLdO9UX5cCFACqLfn8okJUAMZWTOh9ojQK7H+E\u002fTgCBAVhsKxCnPIEBgsBMCwzooQGK6iwSRpixAYU\u002fbGGIkKUDVDOeUwNAnQNjst5qZKCZAlM+zT+ZSJUD\u002fhdViT9IjQAhUSHANmSJAesgvy1mEI0AnOIpK\u002f6ciQLaJIX+7Mx9AGcjqUALdF0CgUnKV5E0aQO24VhLuNx5A6orDYgt9IEBePUn2SCocQMKeWMA5DwVAiCGHqEqME0BqSYLSYTkYQB5SVIRI9SFAhJ3i7uBFKkDDvhHZHrYZQIAt9GIVryVAzsiMsZ\u002fkJEAjRp6Q+3YjQBVuvh3UjCRAt9ZFdjq7JEAs4a+JgOQnQNVKVLxAcSNAE5+mN+OjJ0BQL3J6WTwnQE8HKmIAxiJA+wm4YcK3JUBBSOZMJRQkQBYoGg9iaB1A+s1UDqFbHkDiP+NvnyUmQK+5Y6bqYS1AU+QhlK3EJEABK06r+A0hQMn\u002fLapPNCdAqNN88spzJ0C8fZ1OJUkhQEjgl1rTtiRAICA1CNLAFUCt3eA3JhUgQK2ltj8nVCBA5yWilwUrH0AYhmcBlK4qQIYZEbEtviFA"},"yaxis":"y","type":"scatter"},{"line":{"color":"blue","width":2},"mode":"lines","name":"IF HV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x2","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8WXVQIK30mQIRtr1iJnCZAtXXcBz3OJkD\u002fEo0CT8AmQPxG+jR0OydAnBrJTL1EKEBq7GxQ7S0oQC5avFqU0CdAZAwTPKneJUA53R9xW\u002fAlQKjDUQ+GASZATU0oYJ7pJkBCYgkQROkmQMDR4uAgQydAT1FgYHzlNECZBc4BjF41QC7v6kaJLzxAv637CSfBQUATA+kDO31GQCz9IVGwlUZAxIeLdnMpS0BKnXWI0itLQCdRoMEu2EtA42MlacXPS0CEoQObdnZMQKfVB2cnVExAbAyL5JGNTEBYhIsQLvhMQLDHtnJn6ExAbTSB+THBTEDZDOYmE8NMQD0uP4DKD01A8aYA+iT9TEDAj0FqWf5MQNgzz7NJXExABnwQ77qOTECsP8UmIjlLQLAx5Yb2nkhAaDsDUML5QkC35IKwiSNDQDvR3PG2WTpAUvNFNwZSPEC9faKSGzo7QBRkNV4o2zpAqeT6G5b\u002fOEDM2\u002fMd49Y4QFpY6FRfTzlARs2lI\u002fVRNkBLMu0J52c2QJpNc0xDVzZAQqz3XVBQNkCsBQUoUOY1QEQ7\u002f6etuThAfF8iNYG1OEAyWGC72pY4QH7j5VowHDlA53iwgTpBOUCZ\u002fjLcG305QPsatxFuJzlA6dJVDmn0NkDJVlTLzeo2QKUAoYfxWzJAtaoDK2FnMkC5tpI0qzoyQHkGIT9DQjJABiv9GCMCMkCwZjLIJYMxQCWW66Uk6jJANKwpEKXxMkAODoeiGOMyQNn1yeWA9TJAsTCjSP3wMkDSp5ku3qktQO2OsVinQC1AABi2C1hbLkA0T0jF8zgsQFtC20c5fitAJo\u002fXBRXMKkBWF5\u002fpcWgqQHyu4RfNdS5AVShMiKrEMUBQxUR4OwsyQH8nvMAXPjFA0wAH1N2YMUCDY8w8axAxQItrnhAyDzFAACYd+lt+MED4+XYUbbosQG8naWXDUDFA1bXkP8lSMUBM8zT6VC0xQHkEL1Q7RTFA51o85xVxMUARb6uujHQxQM3a7r9O4zBAvq8ZCsHzMEB3uyjYzqoxQMT5SaDREzJAPcFik8PqMUDrj1T+1ocxQOsJz95WMC9AvCVKCAgNLkBGPsstIwEuQPnzQcYJNC5AmAQu3sIyLkDNRlLNYG8uQNaj7UpnnixAU+2qspl2LUA3pXCOH3IoQCaJua6JFyhAtC6wgnKDKUDglM4wBdgpQLxyFyV\u002fCStAsm3wkcNcK0DMEDHDas4pQPcmfdluyy1AhAYJ\u002fT\u002fjLED12F\u002fwgC4rQDvhky0lIitAixuJkLp7K0BJPe6BDMgpQME58Z02SSpAWLe4OQInKkDAY4C1DWspQLcrznPBmSlATHPIwKDNL0CdNiKBAwEwQBV\u002fQpc5bC9AHy9ACMVEL0DtI8pUNOwvQBFIWOfJXjFACBBxp1h1MUCi\u002fmBGsiQxQJLvWuV17zBAdjVODrXnMEAVZqPjbFUvQOcZEcrMlS9AR7Gf4dyUL0CcnujEEHovQA7rJQh9Vy1AIpRqld0AQ0B6LMsOOt9DQEPFuHc5TkRAFDxzSmGeRECOLDAlJaZEQPcZklZH5UNAGCLZ5JvmQ0DqhQP6vuFDQNsuucaP7ENAThIYH\u002fLnQ0AJNK4twZZDQDClAK7rjkNAjgS6KASOQ0BCo87UBo9DQARJgcW1ikNA0QH2\u002fB2IQ0A8u0TfxoJDQPFo2F04gkNAHGvyg9qwQ0ClsY6F4rJDQNOCVhcEgylAHEJF\u002fqNTJEBAwjfpbYMhQEqeke5fXB1AIcglZcsQIUAJY8bKYAIjQGvwVC4p\u002fSNAQgk1i6MKJEAuToDWcBQkQHhU7xe\u002fCCRAOmMIO8\u002faI0BuG3dO8jElQJbi5IF5hyVAChfWLaUaJkBTg8E0KycmQDFzyVMhdyZAUxFApnaPJkDNFW2C3mcmQA5wdCuwtSRAK4NJ0vKHJECdwS922MsjQLA8eKoMyyNA2IQwyvC\u002fIkC\u002f2aTvcQkkQEPKy1vaACFAOPwioraNIEBNbgAXl+UfQF\u002fbkZiJ3x9A\u002fyUAx4HYHkBX9eH4NDAgQE5Et3JRIyRAbDrshEwTJEAUwDF3pJEmQHGSPLERaylA+HjKGMK+KUBBsC4lPP4pQONI331X5ilAhElandrnKUAS2DuZ58UpQBGKJmd4CCpArBi7xIkqKkCdPhah9GgqQKJUZwKvnipAtyVaN57zKUCW5hTLBBkqQDCr6hibhClAuDhixBmdKUDPeaUcLKkpQFwGpGt\u002fzilAtDiOY8aHKUAPA69YvR4lQKOHo8WaFCVAm7Et2AdBJEA1fLRFlvYgQAboRpj1gyBALGfG3NI1IEDmLj\u002fwDj4gQKapBTKKIiBAodGPTFFsIEAscGIeiZQmQI\u002f627AGQSdA"},"yaxis":"y2","type":"scatter"},{"line":{"color":"red","dash":"dash","width":2},"mode":"lines","name":"IF IV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x2","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H\u002fjsyjqI4wnQLebiBnmsyBA45N0Mc2NI0CZTUUx4KwpQEJvDVSmbitAB4EC5AqwJUCrIbT95McqQFlr9kdf9CdAJHXuLxroL0AOJrK4l84kQPokFbDXJx9AzdHK7Kt2LED6sL5LJn8vQKppGWJjXilAuq1+m3KSN0Ana9pGFKc0QAjaloc2\u002fDtAiDyPP+JjQkCiI5XPuRJHQKMkDM\u002fQ1UVAQlNMbufxSUCxvjRj6jlKQNBjIHQKPEtAX71TvBDfS0DDehFQ+YVLQM831yPLqUtAVwN51yTjTEBht7MKFFJNQAL\u002f\u002f8lmNUxAWz7AX9JcTUBCFB9k1TBMQJ06kyn1hExACsksQzGOTUAnPrIYR9hMQHk65HR53U5AIhsSmVImTEAAxQ721UFLQJIGt3akhElAYHOb\u002f3NOQkBCOXEkZTdDQN6cyDaQ6DxAD4f0B\u002f3PQEAiaYykl605QBxr15TknTdAEL8E7myeOUBsKm3kLQk4QJ8kl31MajdAN8HzHwUrNUAdU60cvak4QKpje7GufDVAGWGSyWb\u002fM0CmTOX6aXo3QFPst25fWjRA2mCW8hvjO0AQ6pg+1Yw4QHc5llhkpzhA6fj2eZboN0BIVrvH34w5QKAWX7\u002fhOzlAT6FTLqFkNUDnq7+4Fdw4QItvWZRVMzRAAoUqGHpMNEBK1etJ2cMxQLxGlQXjDDNA8fP1BzovMECEI5i5de4xQHr5sE2VoTNA+\u002feQPksjOECg47OJB3MyQCkvUhA4XjRAC7FlRdVwMkCJlPqT+zssQH+F6lBz2ypAB2nECc+oLEAyRzBZHronQF2WkWcgczFAHlagPnCQLkApUOV\u002fhzErQFKC+u0toChAElEn3sHhNEC+yQUV7+EwQPdwGlqJ\u002fy5AdLHhTlFwLkBKEXdTm\u002fQ2QOCBPCGfGjBAkp\u002fbsgfrMUAUzeM+nAArQPaPU5usuy9A9u1FzBmWNUBTPVKvF74wQAVMGhL1HC9ARafuyu3KMkD8CRSv5R0zQKBTmEgJaTRAeXEzuyKXLkBEydtL5JQwQAQmE3nB7C9A1EF3OLy8MECBbc1MCE00QH3P98uJkC1AGEGHNjgZKECbVHwavrosQCjl3n7y5jFAe8LU1sZDL0Ctd56ENEAxQJp5z9lPYTJARR0UgXd\u002fMkAhvLR8ujAwQM3sVA00QyVAv+cgaW2+LED4HBy7c6QoQIobUPer\u002fylAioNP5mLwLkBVwNonjogxQGHej4jJZypAYZLK+2aDL0CWlCS7t+QrQAfHkrHp8ihAsyz+269qJkBZ0YuDXyUpQIe+PAaQ6yhAk4m186yhKkByESnQI1AsQGiwjvIteS9ApCJ6vOBtLEB\u002f80629Q4vQHDamqF4jy5A74K0q1pwMUCcFy+8nlgyQLtJTvcIoi1AZuFe2YGhMkA0q1b24O8mQGO\u002f8NkE8DFAXidmr\u002fLjMUD57o0jssEyQJFr4MUeGTBAkBKEp5aFL0CU25VTmHIrQLDeaS\u002fjRzBAgPhcBHyVQkCqV9T35+REQLeLlxv3R0NAohpwPqM\u002fRUBZD79Es6ZEQAcSYPyNRURAbXn4zQW\u002fRUDYjr\u002fEsPtDQDCTc8z1ZUVAK\u002fZe5r5AREDsr\u002fGdRW1FQIiOB5QTQkNA3fKzIm11Q0BQTmq9nqBDQKf\u002ffzEuE0NAEwRd4yDXQkAAVaFUT69EQHq9zP9A\u002f0RA3t4C2HbVREBxJqyBtgVEQLreO+AkLyZAmF5eB2JxKEAUVO6fZC0bQPwtWt3v2Q1Aa0yrDatNJEANF\u002f6PY9kmQHQPAmIvFiVAYZyyvPpDJUCd96Tst1UcQOvugy0HDh5A0sVsNAF2IECCDcE3F3IjQEEoWNGTJi1Ao8TqgSFEGkDLnoQFjlopQKOrkYO\u002ffypAy7M1ypo7JEA1NIUD+hkvQC2nLUluYSJAjy2Rr8VPIEADVUpZmcokQLaYz9dU8yJA0avsjFNHIkCXdehAULUlQA3+PD\u002fCtiZANAK7kc0dHECKMzKC+hEcQOC+zBE7vyJA\u002fefiZOdoIUBeV2sngI4fQAujGGj2FRpACVdxKv1qJUCA20xKayUkQAQdJum\u002f9ShAGejQt0hRLkDS7YYdCZcmQJj5yMMrkiZAmWSLB0UzKEC6QgNfzU4mQOvtpg1sJiJA0SyeJo5iLkB0Namv7pIlQD2C0NcwgStA4NtEIiI+LEAeAzdHvokvQF\u002fMnJOKWSVAvi3CzozyHkCCNDd0vz0uQClLaqr3wy1AGhS6CmqFKUALptdofvwkQIK+8dgA+iJAihNDzUbvJ0A0hwSnYs8jQJoNmzdVOxxAKBNbE9dFHUCmvYWSK5oiQLqrXlSyLSFAqZIwVWV0HEDHde6bs0kmQLimuc8E4yZA"},"yaxis":"y2","type":"scatter"},{"line":{"color":"blue","width":2},"mode":"lines","name":"IM HV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x3","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H81fI5KBUExQEce\u002fv4svjBAjrVqm1e+MEBTbtNN8BgxQHjeGPlPxDFAuhUQdYHuMUDfMQgIpvoxQL6mLQ4RvzFAtGAv9nK+MUACZ+40lAAyQJDaFbL\u002f4DFAHIoaUlVJMkC4OU1CEe4yQCEwyrvvnzJA9vQCMv6BOUBdBHqmt4Y5QKbwvTE+Wj9Aqu1HtscuRUDRroJYnfZIQBy4AAaC6kpA23mkXHDCTkAwgTZ+TiRPQBzXp9vnQ1BAv88Qjy5rUEB9zxkqToNQQPxMfrfzaVBAbcS+PjV7UEBQMIwoVLBQQEe3Jo5BolBA4h3eta58UED42bciS3JQQHggkK0djFBAuCIGYAdbUEAT+03YDlJQQLSVx+YoQlBALNkypjtDUED6nUUmMIpPQII5QPd59kxAeFO6ERJKSUD+Pkoq1PVGQH3HTmnGAkJAsxIlDxrOQUD\u002fh12EHMk+QJZc7VJAWD1AMVdG+BcRPEBaqCAUp0U8QHDfBAvfHz5A\u002fxx2WCNrPEBr00Vl05U9QP8Hipn0lj5AgupMbpP7PkD68QKbwNc+QPP94QyHeUBA8lnde8JnQEAUm8G7ZVdAQA9ZYt4NJUFAiN9M\u002fMAFQUDyVt3VSntAQAVEUR01OEBAIZYKTB+jPUBxDpAglR0+QLLxsgqOrj1A0PEkiqoOPkD8qjjFj5E9QPHwihlH0z1AmWxO3ewYPkBGhSvShKs8QIOoMhtbTTxAuUay+o1QO0DHdl4aUzc7QEqascBtvDpAOfiPZLfHOkDbSzLRlC43QIf1kB9r8DhA7Eb2o++3OEBNXAWlvJk2QAVRJNYEojZADmwsesp0NUBzSY4xe0s0QGT83E89ZjdAK\u002f\u002fr\u002fBhgOEDmTE0xIqA4QO4ad7JwqzdAdy0sfEyaOECGRZ0e42A3QC23jHteiDZADvNt6fw2NkApwTFmSFk2QAcvKwDQqz1ArpslfFguPUAvygm9eQA9QOs8okXWRj1ArtzmFq5KPUCjfil19SQ8QNgjpws\u002f1TtA49iUjgvBO0ApPTy7dyI9QKS7YWX9BT5AeW4F2OI0PkCcgHD7rTM9QKMiy1f+8DtAFgfh7D3BOUD2b8FNY585QNqBLBrTsTlASGxM0HLNOUBFdbgCFrQ5QFmSq\u002fLoEThATb3RDt+OOUBO7YJCO9c0QApQrm3\u002fXTRAzRgijpBfNUBT7ZkAEqM1QPUSmHdHwTVAw0b7j9nZNUCX6r4GF4s1QLz+GsAmdjhAz4pGSWM+N0DsQqPH2oA1QJah36KkejVA2T6yGU9WNUBmOsH05tc0QKISd1595DRADS08gVf+NEBfeM+lcYk0QBsJI809AjVA9rPKcj0UNkCwcH0gKy02QA75hzIe3TRAuEVrIBb4M0D\u002fkw9ymgs0QGgKTgYvEztAg+Lpfa0VO0CqRiMMSxo7QHMMSI61xjpAUBJLu\u002fzROkAjRU+dSiM5QHwYqSZSKzlAxs18xgzZOEAzxZkNNd44QL\u002f4cold2TZAdNzFAt5\u002fREA7x9KJTZ9EQCN71JhrGEVAZCb9A6TzRUDbl\u002f6g0FNGQAtQW5DU1EVAjojw9APWRUDamW6Z38BFQDRn+mnP8kVAosJHbGbzRUB9uiRauaFEQDrYrLLwoURA7vTO5+G6REBINZ45Y79EQNyh8L66wURA9DdE1MW\u002fREDI7AulONNEQF7wquV22ERAiZq1aiBmRUA6Jj2n509FQNTGA5p4PDVAcx1AJfTTM0DHRptXo0IzQIMn\u002fITb2DFAbkrg0u08MUARCcE\u002fiXoyQJmuPkVzfTJAot8NIHaRMUCv2ze0DIExQHUDoQ6kjDFAJ6iGQSpkMEArptQUIDMxQNDmJJmsNzFAVg3g1nrYMEDOVqbT3OIwQAjTvO869zFAjsjw9At2MkDaZd+hcnkyQPDllZ0xXjBAdq9lnxiNMEB0JcjVUS0wQONCd8\u002fdoC9AaIqh9svHLkDc5rkBl2guQM\u002fqB\u002fgwCC5AZeDmHgoJLUBV4We9HDAtQII5S2RI6yxAfrem7+eYLEDhA2OHVu8tQOwugcvidTVA7+eULSpnNUBgAx8UKfA2QDUG1mAA\u002fjdAJ74T5PgDOEBR8i+83MA2QC4YH1wxaDZAQUhztL9nNkCmy2XqjDs2QNpdkHkKFzZAE9N\u002fe+wjNkBkCoQ\u002fccM1QCYmTwRItDZADKXOwDi2NkDSZixDNbg2QHMNSRydsTZAxoD6nJ2rNkBwa9dEKMA2QBt3tV68vjZAiO3SuHmcNkB9cZDQyDItQGHmYzEH8yxAWMVNiM8oKUCihj8240MmQCaFdeAXIyhASHj84mt8KEBHI3A1KqAoQGh24WyFeyhApqjXHmy2KUAk01N8OYsrQAmT\u002flwAHCtA"},"yaxis":"y3","type":"scatter"},{"line":{"color":"red","dash":"dash","width":2},"mode":"lines","name":"IM IV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"xaxis":"x3","y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H9fPe1++UMxQJzompiTzDFA3a4wJEF5LkAuUpbjOfExQBJI+th5HjFAVHhr8GEhMUC21SbynaouQCJVOPeZ1SxAxNj3PSMHMkCytba48GI0QP\u002foc6I8dS9AAiQ1kiYUM0Dedy3SduU1QPlJdiJCuzNATFqq2ITLOEAW65S7Hxg7QDzN5tFI6jtA3GiDMNPMREAb50shsbxJQLgrJupDpEtAi174Non2TkDvSWWNxTtQQCGVB8YJwE5AlTavPRqkUEB8dHmW+u5QQCs2vphEtlBAVrZdEps3UUC3wsmda45QQEOC0RCf8VBAFpbZpNIgUUCTwKL8UzNRQEVGXPX6ylBAswzijysvUEDFEcBEb2lQQAxcJoUlZU9ArTeWYXJzUEBiRX5t4dFNQB\u002fgSZdrdExAR3eL5baiSUC04cFds5lFQF08WZaQUkFAlkK5oAaLQEDJt3BEVAZAQBlJVPGwTD5AOuAdBudxOkAn4jWrHsg9QE38qZ3ZUkBAVUAfR9kJPkDknm8ZCOs8QIR+3Z3k3UBARL3LaK3HP0D1+qIEqd4\u002fQIwonQ7kE0BAS9MkoVGuQEDamY22XQw+QJVqSW1mJkFABONhKNWkQUCmyItsdZpAQGQp\u002fOIGuEBAeKO3EcRhO0DDcZWdQo87QLR0VzqCPDtABhDOAeJ5PEDtwbuw6OU5QNfgkWf4TDxAooR73qESQECGyufQl\u002fY9QEB5xaRfEzxAnMmMkol7PEB5kYmGFrI6QI0rYX9xxjtAO\u002fNp5CzvOkAZ4oMopQE6QHa6f4e\u002f9DpARYWUw2GCNUDJYtD7ceA5QK8Sy19fRTlA73576UiFNUCBwL2tWUk1QCQMHng+6jlA22b7\u002fe2TOUBZ\u002fhbbiIA1QAzcc7K6XzhAP6Z6cl5tNUBynZnr30c5QI4X2ZQQTzdAcnexFK6AMUC+zn91oHUzQICCz1nB2D5AoGRLVGWgPUDfHAjbs+Q9QEmmpwwlWT1AVtom8SUrP0BQfyqYnbg7QF5mGr6e5TxAq8+f5jUGO0D1+H5VhkQ7QCIKcO0CQzxAxGw\u002f76ZPPkB+n2I\u002ffxg7QG6Q2aB1CjpAnMNWUnNBO0A+j\u002f1GwQo6QAV1v9vgiTdACKFsBzBOOkA3PTtiWzg8QLogU87lczRAZLmQc4KiPECvtekckIo0QNTePhCsRzNALryN4Mk4OEBHy\u002fMJGqc2QHFM0yl8jDdAxxOUN3sgM0Dv\u002fULzNo0yQBjVVmwn8zhA6\u002f6LpZEEN0CXJvvN7GU1QCRYB8Wp1jdA9sU8tUt6NEBqFgUdqu4zQCYcKiyiODNASqGHlgysNUBanvJ7qsQyQMhVrIR2uzJAXvzcRIRbNkBoF8EfiyA1QHvdxcjPJzZAYKjsddZQNEArTOeh4ukxQLFvh9bi5jlAj3RxQTzGOkDt+vdKvzA7QI94EHlgtjtAG8YKsgVsOUDn4ZyPwow6QBXtvUITgjhA+kEL7AysOEDW9avdhhk4QJ2UVbqdyzZA4\u002fmwxnCwQ0CC115imAtGQC1VitZPZUZAKBhRMORdRkAJLJE73EVIQI7qe5ilcUZAFkKFqZehRkDjp9tVpLdFQKW3kLg680RAwp+xMn+yRkAZsbIyN5RFQBH0ouyGukRAJvK0jIXIREBk\u002fFCkf6tEQCEqGyNw4ENAVVgsuEkrQ0BbKbCfz7ZFQDgPu7RrckRAuuQz85sgRECaDSHkapNEQHCweqNwhjNA+OYfo+OCMUCzuGtzHPA2QPdsr3Yxty5AhoWVP2NNMkA6GNrUJosxQGRiP1SB7C5AplIdA06EMUCGYD+sfC8xQJyGQobJzS1AhTZ+scdnMEAkyK8uqyMqQORNouzzdjJABGK1RrDKLUDOOfk7ldcvQDORHivj6C9Ao3V2n33aMkC1RctFJfAyQCprvX07GTBAfNLNODnYMkCbKdEuR5UxQHOutQNBRC9AglvO9XlcL0D4hR2rNEYwQFUltvNAgCpAoqWIEm\u002fdJkDDeeHy8CsyQJ60bJMnwCpAUd8rFHeEKUAOhyX7MbolQBSmLEMgGzFAKWTRZvb2N0A7gg855Ro4QOCdXgfOfDlAF6vQaKUtNkCJM46bz4c1QLvryP1YtTRArtgAKYEDOEBYHn2K0A80QHHuOcl7QjlAHRQYOyGQN0BHcZBO0vIyQPwpXPOrfDdAUzOkQdMwNECo3u0DF8Q1QCSOr\u002fJhCDRA03nA83NXOECia6v\u002f8+s2QNfdRwelTDdAdeDBoMcdN0D+BAOUBCIpQMJgfYLO0ixAbwGjLsAdKEBVYGaOYfglQD7yLaf1zyZAkh0MjnR3I0Ceo3EAQfotQEUwQRK7EytAOlbmADKvJkA2DVi1pBwwQIVciBC0FiRA"},"yaxis":"y3","type":"scatter"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"IH IV-HV走势","x":0.14444444444444446,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IF IV-HV走势","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IM IV-HV走势","x":0.8555555555555556,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":400,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"IV-HV走势对比图 (一年期)"},"xaxis":{"anchor":"y","domain":[0.0,0.2888888888888889]},"yaxis":{"anchor":"x","domain":[0.0,1.0],"title":{"text":"波动率 (%)"}},"xaxis2":{"anchor":"y2","domain":[0.35555555555555557,0.6444444444444445]},"yaxis2":{"anchor":"x2","domain":[0.0,1.0]},"xaxis3":{"anchor":"y3","domain":[0.7111111111111111,1.0]},"yaxis3":{"anchor":"x3","domain":[0.0,1.0]}},                        {"responsive": true}                    )                };            </script>        </div>
    </div>
    
    <div class="chart-container full-width">
        <div class="chart-title">📊 IV-HV价差图</div>
        <div>                            <div id="78c91f81-04a5-4634-88c1-78f20636c935" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("78c91f81-04a5-4634-88c1-78f20636c935")) {                    Plotly.newPlot(                        "78c91f81-04a5-4634-88c1-78f20636c935",                        [{"fill":"tonexty","hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H+Y85dA2pnyP1SDnKSXkgVA5H12ZTHJE0CsUJmcwI4AQLhJs7i90wdAsNJz3a6i8j+AoeiC4sv7P9Lskg5upxlAIGHf9K614j8wT\u002fxir28HQCAYWP4AT\u002fg\u002f5JtEUBzSBECAT\u002f0WNbLlv0yChOyAswRAcCd3X9HD\u002fj8g79VLWR8TQFB+uOGN8fE\u002fYATK8zN0FECw5+C63w4ZQNCJkPuVeglAoEqrZMNbA0AARoeqCGAGQNBAh+uQPRJAoGqpTyMg+z\u002fw2Ng39JMWQNjS0\u002fMZJBVAMO9nxcwoA0BA+qEsMHbpP0DZcKpHsxdAAJxaEoto6b+AeXBRS0XQP2ARqSgmq\u002fa\u002fAOVR9jtf2z9Augjg9HsVQKAr4FZp1AxAALSn9mvOBEAA1VcXs4ULQOAuiHXioRdAgJPd9NJ65z9g6M0bb3ALQNgsU4TVUAxAgBd\u002f9wI69z8ANlCoH\u002f7Yv2CSyuniWgLAgHDop\u002fXrAMBAwr5DGYLfP6RNFfUCVxBAeEZ9V4m0CkBcyxxaXSUcQKCC2fYftPA\u002f+A80fgmNEkAg8d+nNxPqv7CosRgnxBJAkDFyMcUbAkDAmCpZpDkWQKAkTCDSZQFA+OYYJ9sfFcAgjS28F4USQKwMwhmFfxFA4K\u002fHsxg2CMCgDf\u002fVVk7uv5jh8bVMcQ9AgKFDSAMnDUDsE4g77p4cQL54t\u002f\u002fiiBdAHDcFq6kQAEDwl2UpgFcGwLAKRjvkP\u002fY\u002f4OhbJLJJ+z+IWZW9Oy4BQOwFSNGQURpAQK1AgUQW6b\u002fgf0AMjhgTQDft89YyqSBAAJrZS9K3sT+AZdUoq5fNP8C7+xuyWxJA3OV03qcfFkBoomJDqBb0P2QKJ2FkMwNAUFETmS6h\u002fb\u002f8gNg8CBwRQAAj77\u002fmV8s\u002fmGEr2rNyAUBgh6V8YBDzP7QEY4EJQxJA4EPSyObE4T\u002fwilN0B9jov0D99EJHxf0\u002fOLfwQV4iFUBQ19rEPXH3PzoJcRth1h1AoACFGFPlD0DYHoeSkijyP5C2\u002f5FcB\u002f0\u002f6EAv12TFC0CIxwr49bYWQD+h9RD7CiFAnqKbjO0fFkBitdCTa80SQAT37s5ViBFAtr5bPzxCFkCg\u002f4YFaCT+vxS8+O1u2RNAGJ1xoFjd\u002fT\u002fgCQhwUzAWQFy3DqzRgxpAqMHcS1zHCUCQWjOeqJ\u002f6vyDb1LMfNN6\u002fiBty7+u2+7\u002fszVDwEUsXQGQ7FJhcqABA3MDNVkwzDMAgLKUJoZTZP3hOqobLXQbAZAAGsFP6BECECN5pMAQaQObsnxC2nCZAtPp5qd1ZEEAA8bOuzNXGv\u002fBpq1m79\u002fG\u002f6IlvAfXs+b9idj7DZkAUQPyHRJ15NQRAkHAxNFX88D8AxLUkkorpP+C+HCv\u002f3+i\u002f0IMrC\u002fSs\u002fL+QqvxxRtT1P4D0nzwmuQdAoPimc75j7L\u002fAT4jzMELRP8gPcK7mIhNAQA0CT\u002foG0T9gg4gIDi8LQAo1iUwsAhTA0K0qlFOUC8AgoEMNtvrgP+ofpon4\u002fBRA4GxTcA3B8j\u002fIzyXWDawVQPBtSApiIxBAiCGgbUo\u002fFUC4SsJYfiYiQMB1RVppFhJAQDCBzD6y7z+wXgwEkGgEQCD\u002f3KOuwAlAkI3oJopEBUBgUja320QTQEBWxrzbNgZA8P5Q0CzXEUCAWEHPcqH2P+DqiAABbBBAgAAJvfYo1T+gk\u002fU6DyMCQCBrXQsCtPo\u002fSKYcXUkgEUAgRzj4qS8IQJDpU1nX7\u002f0\u002f9KYn+TCKFEDdruR4YwMRQJDlKW7and6\u002faOWRh9yt9j8k40t5zTsIQMEu+Y+3WyBALJllfI06F0AAVlSNsLDoP\u002fCmbNUaoeq\u002fqgg3EZl6GECIvX5f0qwCQCh6TH8tmvO\u002fgCVqeqjdxr9+jv4PNzsTQBr913o8AxJAALLZoUu\u002f+T\u002fgimMsmFjpvwAyZ6g6QZi\u002foP8FpkWz6D94t4fVgGoAwOiN8z2kP\u002fU\u002fQLTVF20a5L+YdFnHN4z3P+Beq0Qe\u002fvi\u002f3OZbFFVxDUBAQoSBCLrvPyvyk1QONRJAmvTHLA1RCUDsjMkiUpr+PxCeNlxBTvC\u002foIcmsgV4B0B0puq\u002flV\u002f5vyjq7Bx4avC\u002faKV5\u002fYMu9D90c7AIOmsIwI74a56fsyNAmCdgS0qM9D8JlKEfqK0QwOy8e8HFNhhAdD2Q\u002fbN\u002fDUDoHkBvbRoMQCiNeXTfxQZA3OwTQsDMAMB4gZyMT4YQQOAvEvN\u002fxdM\u002fyCLIWTff8L9Q5xsg4uTkPzC6nhFtnO4\u002fxIVk5fMzC0CYJ44KgmsbQCDNHTUbIRRAYNAWshnQ4b8Q0\u002fjVxIcDQBKx7qrD0w1A9toa73aqDsC28Zv+WXEYwESk97spHBLAhF7fEsw+9z+QrJvkTq3wP4C2IBIY2Pw\u002f"},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H+wPTfx5GbqP6D5yklk7\u002f0\u002fCE++OZ\u002fw+j+is6aaEVIQQEDdaIIcye8\u002fMNmOvoDD4T8Q8L5aQccCQERHwdSRrx5ABqVTW4MXFECsEn2DndAEQAC9Lwpogqk\u002ffIGOTGXeAUBg7Y2U97rmP4DF\u002fjgijsA\u002fwKdCxg1r5b8AMORRcnsOQAjdZQtcFh9AQDj3TBHb+j+A8SiPpksBwHjbzgF\u002f+RJAoHBNWFWfD0CAFSAvAJDUP2Cu1HrcK\u002fe\u002fABvhsxs84D9Qk6nbunYCQIAFBlW32vY\u002foCr5kBpzA0CAvdGWeP0GQGAaOf\u002fOMQRAAF4wrfcB5T8AhspK1LbLP4AfuwSrwfA\u002f4Gs6+aQ2BkBAmK15l7P9P6j7cwb1FRBAsLIacgXyGkCgDHZkkmgKQBAApmPQPhNAwE5dBmYrFkCAHkV4wcsHwKA0j5AYWu+\u002foKYmhDzy7L90vgMOePgZQEiOi6xZBBBAAHKIcvCh+L9A0ToC6Mv4vzBGMIynnA\u002fAQBlhbbF5CcDgOtvWfl0gQLC0N86gDApAgDyLgVbJGEDgCW\u002fXIMjsvyBgCwXUzwZAIFCghWs66D9Abn3l2on5PxBLhElOivC\u002fJP7kyGgnHcCgxB0YI\u002fQFQED\u002fVoeLrPu\u002fVOvORjTIFECgEDunZhf2P1DMh9SAyfS\u002fANpo5sXbzj\u002f8xEXBJS4TQAAscLE6xwlAbBf\u002fNK7CD8BcHW+E4f8UQNDfThHEpQBAQJ6SbrNEJEBAxyywNXjTv5DHiG4MgRRAoHNvg5wW\u002fj9wJVAHcE33v6jJvkAmnfQ\u002f4GCqBED+6T+oJvu8D3ADQEyzrJytUgfALFMflmFTFEDgg2uVWuPQPyg6emCeGBRAYGYu4fOQ47\u002fElTpZl14gQGBA1oNsH+G\u002fUIKZFX2R\u002fj\u002f4216tPEYFwMCz5VuTR9s\u002fkPUu9tpqBEBMDXkBt1QQQNSWTJGdlRNAwHyWOFgW678QmvGte2cBQICVwoih\u002f88\u002faGOke1L8DECgEqFulMkEQCAI1j3qnvc\u002fZE6JVgB8G0AIJQUxUkASQFCu+EQZDQdAQC0oZYuj6j+w2kZxnOLxvzReTFfa4wtAHBMW+jutBMBYunij8y8JQJ63YJK9dxtAWKvGt17b9b\u002fuWq6Zc6EXQADxPQ76VN2\u002ffFYVt7N3A0AgOYLlpVfwPyB8YlO9T+y\u002fiFZiNdt1CkB4AhmLo7H5v5Qj5gq2\u002fBBAyK5iYt8R9j\u002fYe6zgpwL1v6AYnq2NDus\u002fOGtsi\u002fF6A0AGr8a4jI8RQJDtcTXkkOG\u002fqs4wmZcRE0CsD91a8S4AwEAdZEOXwdU\u002fwnMmq1QhGMD4BvZ9Kx4VQOQO+Hbs3QzAgHdUfVwAzj+gQcjNCTnoP87kD3lyORNAJBUDa9vHAkD8+pZU3osNQGDIb0lau\u002fk\u002fgJyK+PFRxj9oVOIcMLMVQFyAF72iTxhAgKayPSWiAEDUU+UJU\u002fgFQHy3RNk6lQNAAI00At5A278AgklBg38QQFT\u002fkZTnZxtAAKS+v\u002fkdoz+AmjK3naP4PyCbGdy0vgZAAPy5qaoEsL+gUcyYnMoYQAAYjFUz+JI\u002fgD+IC9KhB0BAlA+21nj5P+CQ3ls6BwFAIHmGZWeS+T\u002fQPM1PetAYQOBcF5osXA5AEBdFx\u002ffuGUCYclFyJRYYQIAjWuRNN\u002f+\u002fALQNnaAmwb9Q0CJiNLIFQACtJosjSRZA4LZRuXXO\u002fT8g7OMo97kXQGDTvLzKNOO\u002fQFf7URQXwz9ICBxjkFr5P0S7J44CqgVAXOcdWvBTDECA8MmisWy0P2oL41TWjhtAuGgu0yQxAUBAh48CmMHyP+AmbvP9B\u002fg\u002fnPSbHKgqGED0fe2GYnkGQJhd8kYRBAJAcEk5ikYs+r+ufwpsOqoRwEA8+hDk3dQ\u002fYF4SNg033z+AUR7DqHbWPwCc9hAsUOM\u002foPXjK42X\u002f7+AL5TgcqYIQKq32Sy6TwLAqJnaDe3xBkCAifoW04YJQDCIzUxAwPI\u002fbHWUDhySAsDUpXKV6MX7P8D0G5cvuMu\u002fztJLnM9JB0BwfxDQ1Ifvv7j4kQtPZh1AqBWveZugAkBAy6XYiVXmv4AznUek\u002fss\u002fnL1FbeUDAkAAiL25d9D1P6BncmATG\u002f8\u002fEC5fEuV56z\u002fAmBQMyeUMQCCMMCYC6NU\u002fKKhOVQqZCEAkmmVWuuQMQPAQpLXYquM\u002fbDF8+j9xBUBgHRSEm7EXQLwnVp5qPhFAoDUb9+Vx579Yhh+gxIb+P7jUSceqjx5AMJu5nqiRD0DcCq68+mkKQIDvGXfwI9y\u002fxsenTPm3EkBuXjff+w4CwMB\u002fO40G9OY\u002fqJITENNe47\u002f8LyROlaQGwCCJPnVlNOM\u002ffKjfbNVWA0BeWPHnxRIRQEgMTDa5vPE\u002f"},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01"],"y":{"dtype":"f8","bdata":"AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh\u002fAAAAAAAA+H8ALTM+JL3Av3TWXdpRdRpAAPsOc2P24z+sPu208todQHicWtXuPA5AcDyJZN7q8j+QkuZAf6z9PwDnqu5G8dE\u002f0OytkCSt+D+gzj+335DkP8A4khTDOwpAsNX\u002fqNBhFkCQrLbRPoz+v6j3xq6z1w9AXJAjp3GNEkBcu2VuZ2AWQIBons4nBcK\u002fYJ0Z5m2yA8BAoh2P++3iPwCbwW4zDBBA2IfKKGxCFEBAoytV3hIGQGClkSN5+f2\u002fAJjCRBWy4L8AcEjgdemrP0AijcyYjgFAwKYG4xbd\u002fz8g\u002fLdvWSYCQJD3AF5tAhRAcMZgaW9jEkBAkgrDApgJwAAVG9Ghsuu\u002f0N87iI+yDcBADT1eZYf+PwAmhyh6nOm\u002fgOogsOI28z9Yn\u002fCzKpcSwDClRlyPggZAkDHy96VpC0A4ZBCPKBoVQCDtM1W9ZAvAgO0EdHz16r+AtAearnztP6CtWyQrueY\u002fwAFwda0hBkAALihxUYCrPzDkHJN2S\u002fi\u002fuN2QXlDaCEDotAGD0osJwKgrkOKKXQBAgFoeWIhB5z9w\u002fYinhukMQMA9MYxFFAJA8A0fD+G+BUDkBISFwSAWwDhPSB5ipRJAALMyTKqp3j+wnbeDX3ETQKBD1HIerQZA\u002fLDwtEQ4G0DQkyEhANgGwKBV6molyAJAwDkFzM8NAUDglULZfF0GQByQMhBJwhpAmH60wbPrA0Aw3HcCvXb5P+CC2nxPQeS\u002f4Nhh4+AjDED4egM6N7oNQHCMm9NJYvc\u002fcBJ295ZG8T8wT20dTPnyv9THEhwvzxBAUK++M\u002fNJ\u002fT9U+PHBqT0bQLCyF0eoKSFAZL4p60qdEEDAZrZTse\u002f0P1hZYsIVagrAABiPmPIdDEAwlBFfYqcBwLCUxJddIvw\u002fsHnoXCjaBEAAvMkfRUe2P7jQ5kOfNxlAEJt9vdze\u002fj8QuP\u002fqulDyP0AI3g1T5+S\u002fGAXql6QiCcBsNr0qKkYUQOT4Pft98RlAoLr7vP4NB0CQ5SzauVgGQFA+j7RnUQLAoC05xNmB7j8YQ0M5r18BQIibowo4GhJAKP4v\u002f137D0BAT9ebbET7PwAtzYhFRvY\u002fgGAhj0fL0z\u002fwAVdHLcYXQKAjZMlHbeY\u002fGAuKsGRbFkBg9UhF\u002fhIFQCAQrnBgSf8\u002fpN2zOPAnEsAgyxrHdbLzPyDHb8L6lQbAAJvfjGcZ6z+w4iCQZNb1P2yG15RA2RDATKJva8iGF0A4LALQN28OQEAerl\u002fEEg3A0FvNJ0e8BkBQ8tsPXeDyvxAIrZ7ZTA1AwJM3TyXy5z9AAX7Lhx0RQFhqNhBNFwxAGCKLpkFcCUCglj4q00HyPxD657bcfPc\u002fABo\u002fC78a079Ad1+TP3ALQFB3nLYrgxBAaK95FvkAGUAg7ICrKA\u002f7v1hbl9yHSxZAEG8bCTC0AUCIop82fXsYwHRMQEnxrhlAcIvXLFtLC0AUmNERcj0WQHjIXG439gDAqDdjk+9PFEA4DsoqKcUPQAD0v\u002fVxiAFAwA\u002frJICk9T8A2Eh6qpvNv2Bt0ezgwvw\u002fRIIbYQZfIkAw8aPtFbYGQIAyORlkmvI\u002fMAScdCF3CECYIp9JdLkVQKizvrit2BZAwDMq7PqUEUCIRCqBptseQGCL1gWNPRlAeDmj5\u002fcXIUCABYhMdN33v8CkrqUbM+y\u002fOP+3TNh4EcCA8jgWt\u002fn5vxCcN3kwHR9AIJPY0oiBDsAwvC2ezRINQMhXcIFTegfAAK1FcE4bxz8AuR9SHy\u002fUP6jHwu\u002fVUhdAoEDYT36t6D8gMrJLaB\u002fuP7REBCd+ChhAAND7+h926z\u002fQPp8p1q8DQLjzNOUyiAxAwJr2hNrA6D\u002fEzKNI90gQQAA3aMwEw8q\u002f0BroQH2p+b9wYYbMngMHQMiShAondRVA3H5Ogte8FkBAcmw4EenwPxCCSvTHHvK\u002fuG4VCnlwDkDY\u002fVtgD2INQIjwgu3k+P0\u002fcFY\u002fJMmP+L\u002fycVN4v58RwKjQpAP6Av0\u002fJNkvUMF4AkDkV60fvg4FQKhlmwFvuRJAoC2y\u002fVPCDkBgEUV7+cvVv7BwmXPNZgFAAEgjXlS4k78IHcU99lMeQOCpILFixgRAuP8RhlzBAkCAeuBiCf3\u002fv6CLtj4pow1AWP\u002f8NoyaBsCg53hocDP5PxxgCI3zVxBA3JU3MfB7E8DYVSyvzbUFQICn1kqd8whA4HtH2O2vCUAAWVKLzdfXv5jY1DbMbQDAEBX4EDHNCEDYpnmlihwOQJg8XOu5nBxAONp3p2ZpAMAwqmUQq+DxPyxc2hDUggBAgNOMk3Gutz8ElscIYncWQJg\u002fprkXkglAwNqVWXCKB8D8VFyoZa8EQBBSJH85hO8\u002fQBP5C6me\u002f7+obko9geP7v+DCjHhL2+Q\u002f"},"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"价差=0 (IV=HV)","x":1,"xanchor":"right","xref":"x domain","y":0,"yanchor":"bottom","yref":"y"}],"height":400,"hovermode":"x unified","shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":0,"y1":0,"yref":"y"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"IV-HV价差图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"价差 (%)"}}},                        {"responsive": true}                    )                };            </script>        </div>
    </div>
    
    <!-- 波动率深度分析 -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">😊 波动率微笑曲线</div>
            <div>                            <div id="2a31e43a-79a3-4a5e-81fa-b4a01678e0bc" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("2a31e43a-79a3-4a5e-81fa-b4a01678e0bc")) {                    Plotly.newPlot(                        "2a31e43a-79a3-4a5e-81fa-b4a01678e0bc",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":3},"marker":{"size":6},"mode":"lines+markers","name":"IH 波动率微笑","x":{"dtype":"f8","bdata":"mZmZmZmZ6T8+CtejcD3qP+F6FK5H4eo\u002fhetRuB6F6z8oXI\u002fC9SjsP83MzMzMzOw\u002fcD0K16Nw7T8UrkfhehTuP7kehetRuO4\u002fXI\u002fC9Shc7z8AAAAAAADwP1K4HoXrUfA\u002fpHA9Ctej8D\u002f2KFyPwvXwP0fhehSuR\u002fE\u002fmpmZmZmZ8T\u002frUbgehevxPz0K16NwPfI\u002fkML1KFyP8j\u002fhehSuR+HyPzMzMzMzM\u002fM\u002f"},"y":{"dtype":"f8","bdata":"lVB0E6AsO0BucTKN7Mc6QByTkaKf1jhAfm2I9TA+OEB2T76ZWKc5QPlpMB8bYDhADc56F5eJOUA6M9JHHJw4QFZxd99i7TpAGfMwtD6fN0BCPBCNAWM6QGt\u002fyZ1uSzdAej6Zmxi3OUD9ED\u002ffjC86QNPV2MC6dDlA8\u002fm3K4oFOUB1QzwZDao4QJbjqWHemTlAu8XjsgYOO0AIvNhQ3fg4QMxXBtbL5zhA"},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":3},"marker":{"size":6},"mode":"lines+markers","name":"IF 波动率微笑","x":{"dtype":"f8","bdata":"mpmZmZmZ6T8+CtejcD3qP+J6FK5H4eo\u002fhutRuB6F6z8qXI\u002fC9SjsP87MzMzMzOw\u002fcT0K16Nw7T8VrkfhehTuP7kehetRuO4\u002fXI\u002fC9Shc7z8AAAAAAADwP1K4HoXrUfA\u002fpHA9Ctej8D\u002f2KFyPwvXwP0jhehSuR\u002fE\u002fmZmZmZmZ8T\u002fsUbgehevxPz0K16NwPfI\u002fj8L1KFyP8j\u002fhehSuR+HyPzMzMzMzM\u002fM\u002f"},"y":{"dtype":"f8","bdata":"s8yaTEIkOkCVJILbA3A6QPue2hGUCThAqOt1e7+rOEDVxIz929k3QNBUmE3KPThAHGvJV2uIOUDCMLBy7Pk4QN+Yvd+1jjpAoQBDzBkuOkBVMYc5Vdg6QEwnkwd66zdAojGzXDZXOECWWjfHC4I4QJ5SSpD6xTdARy4IZLOMOUDZBgSjILc4QLjQfdDzXTdAVpMK7PU\u002fOUCbP18yztg5QMcVek82SzhA"},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":3},"marker":{"size":6},"mode":"lines+markers","name":"IM 波动率微笑","x":{"dtype":"f8","bdata":"mpmZmZmZ6T8+CtejcD3qP+F6FK5H4eo\u002fhutRuB6F6z8qXI\u002fC9SjsP83MzMzMzOw\u002fcT0K16Nw7T8UrkfhehTuP7gehetRuO4\u002fXI\u002fC9Shc7z8AAAAAAADwP1K4HoXrUfA\u002fpHA9Ctej8D\u002f2KFyPwvXwP0jhehSuR\u002fE\u002fmZmZmZmZ8T\u002frUbgehevxPz0K16NwPfI\u002fj8L1KFyP8j\u002fgehSuR+HyPzMzMzMzM\u002fM\u002f"},"y":{"dtype":"f8","bdata":"Q5Q3b\u002fXmOUD2Ic5SAsA5QOfH270pOzhAfrXJS\u002ftyOUDGUt6p\u002fx85QByOkK+qizhA8iox9W01OUDnfK8NMzY5QIzNbUHs2DZAKIjz2XHLOUDoBTZJTWg2QFVjO8GcgjhAktntVrQQO0C1ytI0iz86QHHxDKo\u002fVzhAHgjtb1sVOEAZAIB6zQ06QCtQSk86ZTlAg28uC+lvOEBKvOBi+v44QOZWTt8C+DlA"},"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"ATM (平值)","x":1.0,"xanchor":"left","xref":"x","y":1,"yanchor":"top","yref":"y domain"}],"height":400,"shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":1.0,"x1":1.0,"xref":"x","y0":0,"y1":1,"yref":"y domain"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率微笑曲线"},"xaxis":{"title":{"text":"Moneyness (执行价\u002f标的价格)"}},"yaxis":{"title":{"text":"隐含波动率 (%)"}}},                        {"responsive": true}                    )                };            </script>        </div>
        </div>
        <div class="chart-container">
            <div class="chart-title">📈 波动率期限结构</div>
            <div>                            <div id="c8259b7e-c6d6-487f-87e2-46239d4f42c4" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("c8259b7e-c6d6-487f-87e2-46239d4f42c4")) {                    Plotly.newPlot(                        "c8259b7e-c6d6-487f-87e2-46239d4f42c4",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IH 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":{"dtype":"f8","bdata":"uko+1dO2LkAbMTQi7EA0QH4gx9xBBjhAAljKNb44PEATXih1V6g8QMaw0GnqFkBAucN6mH8UQUC\u002fxY72S6hBQA=="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IF 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":{"dtype":"f8","bdata":"DU3hEYfDKUAAf95vkaIxQCiKs3FiYzRAhevnq+Z4N0Cp4r05uQw7QJRseQDJ\u002fD5ARQESIlCqP0Be8D8GttdAQA=="},"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IM 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":{"dtype":"f8","bdata":"+z24jT5bNUDRLHqpBhQ4QDTDCGVj+zpA8qyJUJN1PkBHAL3\u002fKSlAQMZsiK5lb0NAGu38tvHOQ0AmnQ6KEyBDQA=="},"type":"scatter"}],                        {"height":400,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率期限结构"},"xaxis":{"title":{"text":"期限"}},"yaxis":{"title":{"text":"隐含波动率 (%)"}}},                        {"responsive": true}                    )                };            </script>        </div>
        </div>
    </div>
    
    <!-- 3D波动率曲面 -->
    <div class="chart-container full-width">
        <div class="chart-title">🌊 波动率曲面 (3D)</div>
        <div>                            <div id="98e1e913-3349-46a2-9d96-88ac0cd86acf" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("98e1e913-3349-46a2-9d96-88ac0cd86acf")) {                    Plotly.newPlot(                        "98e1e913-3349-46a2-9d96-88ac0cd86acf",                        [{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IH","scene":"scene","showscale":true,"x":{"dtype":"f8","bdata":"mZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmZmZmZmZ6T86qIM6qIPqP9u2bdu2bes\u002ffMVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkyRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002f","shape":"8, 15"},"y":{"dtype":"i2","bdata":"BwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4APAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQADgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0B","shape":"8, 15"},"z":{"dtype":"f8","bdata":"1W55zWujMkCBXXmPUjMwQKNhrgOvKzFAVeiK08rIMEA\u002f5U\u002fCX4MzQLV74GIh7jBA3xShCmW6MkAJ3r1QdL41QHEDeEHoQTRACxUyqbbgMEDyCdkPqhoxQL2tLvHKwzJAp+Apbj4uLUC+JtDEE0swQHjOt7AAHDNA\u002fAigVzZXNUBXeL2we1o5QLgsPiAQKDRAAMnjeOo5M0CZnYbhyHszQOWqVTgCQDVAK1a4qjTgNEBiiculZ6Y5QOItQAo0ZjRACbN7DrJUNUC5DwFR+Ac3QLiVEIacGjVAlPkGKS2BNkCt2qK14kU2QOd3BnyCQzdAUzAR\u002fpCVNkB6+F3\u002f2eg6QHxZe5Rx7ThABFb1rEWBOUD7I6zV1kw4QGjtP8nKWTxAniHoiv3pNEA32KlGc8w7QKrjlipwpjdAoWQwFzMXPEAJuMgbEoQ7QJ4WA2EcKTtAagTQWD\u002fcOkBpg7gTk9M9QHfQ0l7INThAgs4RtQS0O0CVw3arpHI7QKJBw0UmhT5AdtKzmH7\u002fOUB\u002fs4MFwY8+QEiAzfzn6ztAujBCK6ZWOkDnzswpf086QGOop7le4TpAVhCK2LuGOUDGw1U+RZE9QFYFUzTITD5A2lIiaFCWPkC205CCs4E7QHHyeABKez1AtpGJ\u002fknnPkAv1O2mwhVAQJf0CPE8lj9AWnSdMuLNPkBw5Kxxj9k6QGCLCj9LMT5AqcGDEBW5PkBus+UIgBo9QHZn+uF+9j1ABETZL2HtO0BA\u002fDs7oOw9QOKnfU6qoj9AwrqllwhwQUCtR6kB88I8QCjybFhZ2D5AFcBbPMo3QED2lbc7pQhBQE8HdLaS20BAVAq4MargQUCr\u002fo43gf1BQIggOEyWKEFAElcrBHbeQEAraEJEdipCQHXk4TDMKUFA3qVjXR2DQkB9\u002fazJosM+QPDtuASh00JA+2rW0LV2QEDQrxBjOQpAQFmdCR54IkFA4SuuEoQHQUC817USfbVCQOnNQcvooUFAuc7rR4FYQkDPu5LZbQ1BQA+t6ap1yUFAdtD5SRUYQkDai2xR\u002f81BQLxH9wG4lkNAD+TZ8l6YQkB8mkbKxDtCQH6ndTysM0BAb\u002f1YpSl5QkCHU\u002fy+SmBDQM\u002frjXvKgEFAZO2N1Ja\u002fQkBmaxPKY7w\u002fQM4re\u002f1omkFAgTVtS1+6QEAFQB0qOuNCQH8F0kbjnEJAd075tB+lQkAfCObGTk9CQLURap0cxUNAq1OW\u002fL+HQUBUyl+fmLVCQLOJ7t2PaENA2kPsrV2rQkD3Yvtvis1CQEgy68glH0JA","shape":"8, 15"},"type":"surface"},{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IF","scene":"scene2","showscale":false,"x":{"dtype":"f8","bdata":"mpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T87qIM6qIPqP9y2bdu2bes\u002ffcVXfMVX7D8d1EEd1EHtP77iK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1AHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fiK77iK77yPzMzMzMzM\u002fM\u002f","shape":"8, 15"},"y":{"dtype":"i2","bdata":"BwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4APAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQADgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0B","shape":"8, 15"},"z":{"dtype":"f8","bdata":"QLexTlGfMEDj+EWgcKY0QMzQRD2AazRAbBgLiedJLUDiz6eus7gtQIR+X27zpixAtJ+g9gRzNUBXgZPPE+YxQEHbEH6IoDFABfyANf7dMkCoIac9jr0vQB2fRjBYfjFANHu3vCQfL0BHwOgLdDozQKFmZiNjODBA5nUkeZ5PNkA5m33QuZo3QLmEc0W5KDVAmsqMS6P4NkAIgNon2NcyQIHtcTVI1zJAbTyO+R+jNUD5RyY2csM0QB5wduvcXzJAX9JwnIhBNECL6Hsykqo3QLGzm7yG6DNAHV1PNplxNUDiPbOOb7M0QNoEnHq\u002f+TVAlWI5qTwZOkBuq4TN63k6QHGhN9uxPjpA8a1iWvPHOkA2W2M8Bu05QHBxZ+GGbDlAJKy9Rt36N0C0wah1RdoyQB3XtbLENzhAHk+hYx1xN0D1e3vcxKo3QDfqZ0XkpzVAuTNFoqi0OkBpQB1+45A1QDhSxHitPDpAcAEBMSNlPEDtxMgts589QPEP3xAQaT5AeH4iM4pOQEBZiUUdYkg\u002fQDaZGvaWnT5ABBy4vVRdP0DDi57VOZA8QH6jcyCRpzxAXzG8MrKJO0ABx\u002fHS+SM9QFkJpsOC0j5ArHwRgJWhPEDGVHwsDMI8QC4U8G6zljtAefqkAY0KQUBBUbB5\u002fNI+QN7bAu3o0T9AtxEqUwo6PkDDWm60MX4\u002fQGI7gfA04zpAF0IC\u002fF7EPkDgL5VpR0I9QF5CtfsMj0BAs4TI6ecJQEAdUZWrlQE+QOfXax0BLT1Ad21sb37PPEB0wVUsCuk\u002fQDBjKfXpgT1AfSJQlpPhQkBkrVj1GRNAQMRrbf2Xa0BAyrw3c6cIQEAVmUo4UB0\u002fQOqmcybjYkFAyPEaGEwsQECikSoPFyNCQEG9MP7mA0FAqeulgZUyPkC8BQHksyZAQDepQkfN\u002fEBAfy9XNP1PQkDtwu7RwJdAQFK8LKjq10BATiQujyijQEDFA3zDz1tDQBUPSn7HrD9Am\u002fc5zYVHQkApAo79gPlBQOkyH\u002fViSkJA3ZKqnIWYQkAMX3ksLgVDQJPGuLcLAUJADGUEWI5BQEDf6OMUO5RDQHJe98K5aUFAt9Vs7XT5QkDxOYOUVdJBQMgRP5qg60JAeQAdPpThQkCa1qyeUHFCQKwKCFDLs0NABHvRbsBGQECRAEpUYGVCQJoyC8\u002fzvUJAgmsxbioRQ0AY8Udp7x1CQHIwHxdqxUBAGv\u002fmyS0KQ0C4TKDfCoxBQF+e2vaoYkNAI\u002f9hodIqQ0BjujzPodBCQKej7oiHbERA","shape":"8, 15"},"type":"surface"},{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IM","scene":"scene3","showscale":false,"x":{"dtype":"f8","bdata":"mpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002fmpmZmZmZ6T88qIM6qIPqP9u2bdu2bes\u002ffcVXfMVX7D8e1EEd1EHtP7\u002fiK77iK+4\u002fX\u002fEVX\u002fEV7z8AAAAAAADwP1EHdVAHdfA\u002foQ7qoA7q8D\u002fxFV\u002fxFV\u002fxP0Id1EEd1PE\u002fkiRJkiRJ8j\u002fjK77iK77yPzMzMzMzM\u002fM\u002f","shape":"8, 15"},"y":{"dtype":"i2","bdata":"BwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4AHgAeAB4APAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAWgBaAFoAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQAtAC0ALQADgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BDgEOAQ4BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0BbQFtAW0B","shape":"8, 15"},"z":{"dtype":"f8","bdata":"l2S03yItMkCmGMPsv4kyQFhWNqgpIzFA4UPNJxFyMUBD0kHe7YEuQG5HqrX6HylAqjMbhqNPMkDrMv5iM\u002fQvQDg2zlsWhTJAbv+glV2gM0BHAB6LAsQxQOcX0wuVXzJAiLzL20f2LUC34F4pDZ8zQFThjIZ+Pi1AZUGn5mSZNEBUkoAuSnczQP\u002fhknKhcTVANIn5mq8TNUClSWoTRSsyQCbLy4kQAjRACerzHutoMkBOddWkFO8zQAYRfrLb3DVAmcQz3aMjMkDyHidgdpo4QPX\u002fskYhUjRAHv9SAFm9NkBYr3wat\u002fo5QNhWCNLgRzJA5dT6O4JzOEAk\u002f7rsprA4QM5ZXzSPVzdAC68J+EJaO0BdZdvDsLE4QBJ+JqfnljtAUsLa\u002fWbmN0CaTTIrZ7U5QOnA+hurIDhAnAQmmAovOEC+xigBloc5QC9W+EfoSTpAqLeehOZ+N0CE1SNmd5w3QHjrWvOtFjhA33yeqGySPEC6FQ+Th+85QGq15rzyIDxAW4yfUN7aPkAShk0N09s5QCljrLDy\u002fztACfEd9BwAQEB4boEpJUg7QP8aYcOEqDpAzKcUvb8KPUCQ7lxzs4c8QJWZzMb3TT5AXym3CmR9OUDqRel6KN88QJOch3zUEz1A4vxpaR22PkC8quir81s9QJYGTVrFGj9AVvQUb86DQEAomT8\u002fM7w+QOcOe1+E2j5AToMroKw\u002fP0C7rtsjDAo+QNPk1vueKD1AAhpjPGgnP0AtMmHohsRAQBjGEoqR7T9ASqW4HGQ7PkDGsmvxBZRAQOKxWAoTXkBAxLTIqlllQkCWjJdHdHw+QNoWNc9\u002fTUFAikj7KH7cQkBhF+X1TsJCQP156l9s70BAo0juwdhkQEDWyg1XSShAQDBVODJn7kBAuJ9luRXpQECBKmTLenJBQAEAjy4Y2UBADo8Xs7xrQEBeIzc\u002fPeFAQAuHaHlI+EBA+QQWbtWvQUB\u002fVl6TjbNAQDqPm44e5EFA9336y619QUBSYK8kV65BQI5ckNWs+kBAt2D2yT6VQkA0ZTWCaV1CQAOAoCoULj9ACLyf2S75QkB7JkM2VSBCQBw0gcm0fEJAchbMekOxQkCREKv+vd5CQJ9vEeZoAEFAzV4gjhl2REDTbbj2LzJCQCPmT+3FhUJALnRpZWCYQ0BFl6cKqxhBQHmVtuDyLURAfHrPNiYfQkCTv+WOBTtCQNWZB2+KMkVA5E2W4E0uQUAHkIFLVPpAQB4Gs3enMENAN6k+m0DTQUAkFiHWiu9BQMg+VdbkvkJA","shape":"8, 15"},"type":"surface"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"IH 波动率曲面","x":0.14444444444444446,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IF 波动率曲面","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IM 波动率曲面","x":0.8555555555555556,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":500,"scene":{"domain":{"x":[0.0,0.2888888888888889],"y":[0.0,1.0]},"xaxis":{"title":{"text":"Moneyness"}},"yaxis":{"title":{"text":"期限 (天)"}},"zaxis":{"title":{"text":"隐含波动率 (%)"}}},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率曲面 (3D)"},"scene2":{"domain":{"x":[0.35555555555555557,0.6444444444444445],"y":[0.0,1.0]}},"scene3":{"domain":{"x":[0.7111111111111111,1.0],"y":[0.0,1.0]}}},                        {"responsive": true}                    )                };            </script>        </div>
    </div>
    
    <!-- 期权分析文字 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>🎯 期权市场分析</p>
<h3>📊 PCR (Put/Call Ratio) 解读
PCR指标是重要的市场情绪指标：</li>
<li><strong>PCR > 1.3</strong>: 市场偏悲观，看跌期权交易活跃</li>
<li><strong>PCR < 0.7</strong>: 市场偏乐观，看涨期权交易活跃</li>
<li><strong>PCR ≈ 1.0</strong>: 市场情绪相对平衡</p>
<h3>🥧 持仓结构分析
期权持仓分布反映投资者策略偏好：</li>
<li><strong>看涨期权</strong>: 直接做多策略</li>
<li><strong>看跌期权</strong>: 对冲或做空策略</li>
<li><strong>跨式组合</strong>: 波动率交易策略</li>
<li><strong>宽跨式组合</strong>: 大幅波动预期策略</p>
<h3>💡 交易策略建议
基于当前市场状况：
1. **波动率交易</strong>: 关注IV-HV价差机会
2. **方向性交易</strong>: 结合技术分析确定趋势
3. **套利交易</strong>: 利用期限结构和微笑曲线异常
4. **风险管理</strong>: 动态调整持仓和对冲比例
</p>
        </div>
    </div>
    
    <!-- 期权持仓分布 -->
    <div class="chart-container full-width">
        <div class="chart-title">🥧 期权持仓分布图</div>
        <div>                            <div id="d880fe95-6ad5-47fc-bd7a-46eac4695053" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("d880fe95-6ad5-47fc-bd7a-46eac4695053")) {                    Plotly.newPlot(                        "d880fe95-6ad5-47fc-bd7a-46eac4695053",                        [{"domain":{"x":[0.0,0.2888888888888889],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#ff9999","#66b3ff","#99ff99","#ffcc99","#ff99cc"]},"name":"IH持仓","showlegend":true,"textinfo":"label+percent","values":{"dtype":"f8","bdata":"\u002fg7tG24vR0Cq3CtAzUomQLkm+q91ohdAmYA\u002fAHo6PUCbpUfvlmIdQA=="},"type":"pie"},{"domain":{"x":[0.35555555555555557,0.6444444444444445],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#ff9999","#66b3ff","#99ff99","#ffcc99","#ff99cc"]},"name":"IF持仓","showlegend":false,"textinfo":"label+percent","values":{"dtype":"f8","bdata":"2HEEjEFqN0AtlZuSJf1MQAUhixTa8xxA+isS7jduHkCChOhwdhcOQA=="},"type":"pie"},{"domain":{"x":[0.7111111111111111,1.0],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#ff9999","#66b3ff","#99ff99","#ffcc99","#ff99cc"]},"name":"IM持仓","showlegend":false,"textinfo":"label+percent","values":{"dtype":"f8","bdata":"HjEulgm2RkBLvPvcR\u002fpAQCtLhB6ZtCNAT1gbsux1JUCo5aa4YjSkPw=="},"type":"pie"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"IH 期权持仓分布","x":0.14444444444444446,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IF 期权持仓分布","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IM 期权持仓分布","x":0.8555555555555556,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":400,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scattermap":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermap"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"期权持仓分布图"}},                        {"responsive": true}                    )                };            </script>        </div>
    </div>
    
    <div class="footer">
        <p>📊 中国股指期货报告自动生成系统 | 生成时间: 2025-08-03 21:35:24</p>
        <p>💡 本报告基于一年期历史数据生成，包含IH、IF、IM三大股指期货合约的全面分析</p>
        <p>⚠️ 本报告仅供参考，投资有风险，入市需谨慎</p>
    </div>
</body>
</html>
