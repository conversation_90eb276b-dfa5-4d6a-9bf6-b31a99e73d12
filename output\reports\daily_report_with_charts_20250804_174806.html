
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国股指期货日报 - 2025年08月04日</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.95;
        }
        .content-section {
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .chart-container {
            background: white;
            margin: 25px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
        }
        .chart-title {
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .grid-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin: 25px 0;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        .text-content {
            font-size: 1.1em;
            color: #444;
        }
        .text-content h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .text-content h3 {
            color: #555;
            margin-top: 25px;
        }
        .text-content ul {
            padding-left: 20px;
        }
        .text-content li {
            margin: 8px 0;
        }
        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #333 0%, #555 100%);
            color: white;
            border-radius: 15px;
        }
        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 中国股指期货日报</h1>
        <p>2025年08月04日 | 数据期间: 一年期 | 合约: IH、IF、IM</p>
    </div>
    
    <!-- 市场概况 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>📊 市场概况</p>
<h3>🎯 主要指数表现</li>
<li><strong>IH (上证50)</strong>: 2770.40 (+0.58%)</li>
<li><strong>IF (沪深300)</strong>: 4052.80 (+0.58%)</li>
<li><strong>IM (中证1000)</strong>: 6642.60 (+1.53%)</p>
<h3>📈 波动率水平</li>
<li><strong>IH 20日历史波动率</strong>: 9.87%</li>
<li><strong>IF 20日历史波动率</strong>: 11.52%</li>
<li><strong>IM 20日历史波动率</strong>: 14.11%</p>
<h3>🔍 市场特征</li>
<li>从一年期数据来看，三大股指期货呈现不同的波动特征</li>
<li>IH作为大盘蓝筹代表，波动相对较小</li>
<li>IM作为小盘成长代表，波动幅度较大</li>
<li>IF介于两者之间，反映市场整体情况</p>
<h3>📊 技术分析要点
1. **趋势分析</strong>: 基于一年期价格走势，识别主要趋势方向
2. **波动率分析</strong>: IV-HV价差反映市场情绪和期权定价效率
3. **期权流向</strong>: PCR指标显示投资者看涨看跌倾向
4. **期限结构</strong>: 不同到期日期权的隐含波动率分布
</p>
        </div>
    </div>
    
    <!-- 核心指标表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 波动率核心指标表</div>
        <div>                            <div id="b4d70e87-38e4-43f7-bcf9-ef38228af4fc" class="plotly-graph-div" style="height:250px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("b4d70e87-38e4-43f7-bcf9-ef38228af4fc")) {                    Plotly.newPlot(                        "b4d70e87-38e4-43f7-bcf9-ef38228af4fc",                        [{"cells":{"align":["center","center","center","center","center","left"],"fill":{"color":[["#F2F2F2","white","#F2F2F2","white","#F2F2F2","white"]]},"font":{"size":10},"height":30,"values":[["IH","IF","IM"],["13.3%","13.3%","16.4%"],["-10.00%","-9.36%","-10.39%"],["27.2%","24.7%","6.1%"],["(无数据) (分位79.7%)","(无数据) (分位70.6%)","(无数据) (分位82.6%)"],["IV-HV价差仍处高位，期权定价偏贵。","IV-HV价差仍处高位，期权定价偏贵。","IV-HV价差仍处高位，期权定价偏贵。"]]},"header":{"align":"center","fill":{"color":"#4472C4"},"font":{"color":"white","size":11},"height":40,"values":["合约","隐含波动率(IV)","IV日内变动","IV历史分位","IV-HV30价差及分位","IV评估与今日关注点"]},"type":"table"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"波动率核心指标表","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":250,"margin":{"b":10,"l":10,"r":10,"t":50},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"font":{"color":"#333","size":16},"text":"波动率核心指标表","x":0.5,"xanchor":"center"}},                        {"responsive": true}                    )                };                            </script>        </div>
    </div>
    
    <!-- 价格走势和PCR -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">📊 股指期货价格走势图</div>
            <div>                            <div id="f15d523a-6547-4bd9-8ecb-2a41765a04d3" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("f15d523a-6547-4bd9-8ecb-2a41765a04d3")) {                    Plotly.newPlot(                        "f15d523a-6547-4bd9-8ecb-2a41765a04d3",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[2331.0,2312.6,2312.0,2319.6,2315.6,2317.6,2316.4,2303.4,2333.6,2342.0,2353.2,2342.2,2334.6,2334.0,2352.6,2349.6,2350.0,2330.0,2314.6,2337.0,2299.6,2289.0,2269.4,2272.8,2261.4,2233.0,2232.8,2220.2,2211.2,2204.4,2218.4,2231.4,2228.0,2239.0,2360.0,2384.4,2522.8,2653.6,2858.4,2848.8,2655.0,2750.2,2689.0,2718.6,2653.0,2650.8,2607.0,2696.2,2690.8,2706.0,2718.6,2691.0,2692.4,2698.4,2679.6,2645.2,2645.0,2665.4,2690.2,2745.2,2720.6,2826.8,2780.4,2761.0,2716.0,2735.0,2712.6,2676.0,2685.2,2674.4,2680.6,2687.6,2603.6,2584.4,2597.0,2628.2,2613.8,2625.0,2639.4,2650.4,2644.6,2635.0,2663.8,2667.6,2692.4,2679.0,2710.4,2638.0,2635.8,2651.0,2669.6,2660.0,2650.4,2671.2,2702.4,2710.4,2707.2,2703.0,2722.8,2678.0,2616.4,2589.6,2577.6,2592.0,2597.0,2580.0,2561.0,2557.2,2599.0,2593.6,2579.4,2585.8,2593.0,2589.2,2553.4,2570.0,2591.0,2569.8,2559.0,2583.8,2617.0,2626.0,2616.8,2644.8,2648.8,2668.4,2667.4,2655.0,2667.0,2653.0,2689.8,2675.0,2646.6,2668.4,2685.0,2646.2,2628.0,2620.0,2637.0,2685.0,2681.6,2664.6,2675.4,2670.0,2663.8,2747.0,2737.2,2740.0,2750.0,2721.2,2671.8,2689.0,2690.0,2676.0,2690.6,2677.6,2666.0,2661.8,2656.2,2658.0,2441.2,2513.2,2564.2,2582.2,2595.6,2606.6,2608.8,2620.0,2633.6,2633.4,2631.4,2637.4,2627.4,2635.8,2628.0,2628.0,2621.2,2609.2,2629.6,2646.4,2664.8,2666.2,2686.6,2688.2,2737.6,2723.0,2695.2,2688.8,2699.4,2712.6,2715.4,2693.0,2684.4,2668.6,2665.4,2673.6,2667.2,2668.6,2673.6,2674.0,2673.6,2676.6,2660.0,2682.4,2682.2,2665.2,2678.4,2680.8,2677.4,2659.2,2635.4,2652.8,2682.0,2722.6,2711.0,2680.0,2689.0,2689.8,2696.8,2701.8,2717.2,2708.0,2727.8,2724.0,2740.4,2751.6,2747.4,2734.2,2730.4,2741.4,2767.0,2771.2,2796.8,2802.8,2816.6,2796.8,2805.8,2814.0,2820.0,2777.0,2754.4,2770.4],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[3342.8,3336.2,3332.6,3339.4,3325.4,3325.8,3330.0,3304.2,3339.8,3335.2,3348.2,3320.0,3312.4,3301.8,3321.8,3320.8,3304.2,3281.4,3275.2,3322.4,3259.8,3267.0,3250.0,3254.0,3226.6,3189.2,3188.2,3183.0,3174.8,3158.0,3171.6,3198.4,3193.6,3206.8,3359.8,3404.6,3588.0,3827.8,4160.6,4261.2,3934.0,4010.8,3904.4,3963.2,3852.2,3832.0,3784.4,3934.4,3942.4,3968.8,3982.8,3937.0,3958.0,3972.2,3940.0,3896.0,3893.0,3905.0,3959.0,4068.0,4040.8,4183.8,4112.2,4133.2,4083.8,4112.8,4041.8,3975.2,3954.6,3973.2,3980.0,3989.2,3860.8,3839.0,3836.0,3901.4,3872.4,3911.0,3942.0,3945.6,3924.8,3918.6,3968.0,3963.4,4000.4,3994.0,4038.8,3935.0,3911.4,3922.4,3940.2,3944.8,3936.0,3937.4,3988.8,3986.8,3990.4,3987.0,4003.4,3921.0,3817.8,3779.0,3762.0,3789.6,3781.2,3772.2,3728.0,3724.0,3816.8,3794.6,3792.2,3807.2,3827.0,3831.6,3790.8,3798.6,3843.8,3795.0,3788.8,3841.6,3897.8,3899.0,3883.2,3918.4,3910.0,3944.4,3948.2,3918.2,3939.4,3926.8,3981.6,3967.2,3928.6,3957.2,3973.6,3899.8,3886.6,3882.2,3894.6,3954.2,3943.8,3917.8,3930.0,3923.2,3904.8,4012.8,3996.0,4005.6,4013.2,3977.6,3882.8,3900.2,3904.4,3891.8,3904.6,3884.0,3858.6,3859.6,3857.6,3837.0,3452.6,3543.8,3614.0,3673.0,3686.6,3694.8,3690.4,3690.8,3710.6,3708.8,3733.2,3733.0,3734.0,3736.8,3739.2,3730.6,3724.8,3716.2,3766.2,3785.0,3816.0,3808.6,3853.0,3851.0,3907.4,3872.8,3846.0,3843.2,3865.4,3881.2,3879.6,3846.2,3831.2,3809.2,3805.0,3832.8,3822.4,3824.8,3842.4,3852.0,3855.4,3867.8,3841.0,3878.8,3883.6,3856.4,3869.8,3868.6,3872.2,3840.4,3773.6,3797.4,3852.4,3922.8,3904.2,3876.6,3885.8,3886.0,3894.2,3918.0,3936.0,3918.0,3958.6,3952.8,3972.0,3993.4,3985.8,3980.6,3971.0,4011.8,4041.8,4064.8,4109.8,4109.2,4141.2,4116.0,4122.0,4141.4,4136.4,4057.0,4029.6,4052.8],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003e收盘价: %{y:.2f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM主力合约","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[4670.6,4729.8,4719.0,4735.0,4687.0,4678.4,4689.8,4641.6,4637.4,4606.2,4622.8,4537.0,4522.0,4473.2,4475.0,4489.2,4442.2,4450.2,4527.4,4621.0,4515.6,4547.4,4536.2,4577.2,4497.2,4457.4,4473.6,4474.8,4468.4,4416.8,4405.0,4487.4,4422.6,4437.0,4653.2,4676.4,4937.4,5362.2,5813.4,6219.8,5749.8,5664.0,5423.0,5613.0,5481.4,5527.2,5490.0,5728.0,5822.6,5850.6,5866.2,5832.0,5979.4,6044.0,5951.0,5975.0,6063.6,5890.0,6035.8,6261.4,6268.2,6389.0,6381.8,6490.4,6407.8,6397.4,6239.6,6103.0,5954.2,6093.0,6190.8,6187.2,5951.2,5992.8,5914.0,6086.8,6036.8,6156.0,6266.8,6265.4,6171.8,6255.6,6333.6,6264.0,6358.2,6430.2,6477.8,6359.0,6263.4,6143.6,6189.8,6218.0,6141.2,5987.0,6053.4,6001.8,6052.8,6067.4,6057.8,5848.6,5699.4,5553.4,5527.2,5599.0,5565.4,5561.2,5456.0,5480.8,5747.6,5702.4,5720.4,5778.0,5816.4,5845.6,5806.6,5776.2,5920.0,5790.6,5832.2,5988.6,6095.4,6143.6,6133.2,6223.0,6175.0,6205.2,6219.2,6110.2,6240.0,6254.4,6399.0,6366.6,6355.4,6420.8,6415.0,6230.4,6215.4,6279.2,6313.0,6475.8,6452.8,6427.4,6490.0,6513.8,6426.0,6562.8,6543.4,6577.0,6550.0,6534.2,6177.4,6142.8,6115.6,6129.4,6143.4,6078.4,6049.0,6064.2,6100.0,6031.8,5432.6,5313.6,5429.6,5578.2,5672.2,5693.0,5680.4,5603.0,5653.8,5642.0,5770.0,5769.6,5820.0,5767.4,5786.6,5729.0,5773.6,5801.4,5953.2,5955.2,6018.8,5945.2,6037.0,5996.6,6043.0,5949.0,5933.8,5975.4,6019.4,6000.6,5950.0,5872.0,5925.0,5915.0,5899.0,6031.0,5966.0,5998.0,6054.0,6101.6,6100.2,6167.8,6113.8,6144.0,6156.8,6084.2,6127.4,6130.0,6124.2,6050.4,5787.8,5851.2,5995.0,6119.6,6097.8,6110.8,6148.6,6133.4,6117.0,6135.8,6112.2,6111.6,6235.8,6214.0,6231.6,6319.8,6302.2,6277.4,6298.0,6390.4,6413.6,6463.2,6515.4,6500.0,6618.6,6605.8,6602.0,6662.0,6604.2,6538.0,6542.8,6642.6],"type":"scatter"}],                        {"height":400,"hovermode":"x unified","legend":{"x":0,"y":1},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"股指期货价格走势图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"价格"}}},                        {"responsive": true}                    )                };                            </script>        </div>
        </div>
        <div class="chart-container">
            <div class="chart-title">📊 PCR走势图</div>
            <div>                            <div id="4293b0b1-96a1-4548-b26b-da53fd689419" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("4293b0b1-96a1-4548-b26b-da53fd689419")) {                    Plotly.newPlot(                        "4293b0b1-96a1-4548-b26b-da53fd689419",                        [{"hovertemplate":"\u003cb\u003eIH PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,0.5135177425749575,0.5470841076313622,0.5262211911675069,0.5350401231039269,0.5341733682034244,0.5380293609061729,0.5357368919752599,0.5533251274651626,0.5330591672450875,0.5337114049969663,0.5415655512639519,0.5462740015425689,0.556384656340452,0.5857511553316957,0.6051056373490988,0.6014731489321792,0.58596716121989,0.5938408247092382,0.5904023036514546,0.5756952964211008,0.588358105109223,0.589942853075619,0.5706168374103314,0.5595821118710235,0.5574485667969452,0.5682890007497231,0.5776316415392333,0.586785617163194,0.5951926513572641,0.5953848146238989,0.5705558417730604,0.573443003322015,0.5716958037195905,0.5727727977451711,0.5910529517909012,0.5939416601067017,0.6039326781111762,0.5918312023099512,0.6083707466660044,0.6061254672724566,0.6339489628028646,0.6480983899665944,0.6781345958272096,0.6691803002830432,0.6877378522324611,0.6758754555291544,0.6427380353589391,0.6280582255814995,0.6418365987644001,0.6259021809351876,0.6273005590792418,0.6311396916667371,0.6435057836039374,0.6424674539318405,0.6515874694617819,0.6582969988915266,0.6581046516269593,0.6593951740795587,0.6766318695915032,0.676038242585143,0.6971391819282785,0.7179713993564187,0.7339343885592113,0.7326451701740013,0.7560410165609751,0.7395982500469055,0.7403956456432548,0.7422045170684475,0.7167629151639566,0.6875426922127816,0.6780673543100222,0.6859606631656081,0.6592794553178318,0.646987617601291,0.6457176335906067,0.6468313346267255,0.6490220000146978,0.6554708701247873,0.6828187409200787,0.6929631620625097,0.7283408335704338,0.7095933619114645,0.7114676660354655,0.6932949778057697,0.7002247813660811,0.6848381729959774,0.6837370022040605,0.6953124926764727,0.711002227936984,0.7126355104558137,0.6946412124030527,0.7056141617761782,0.6865915319270213,0.6930076697303228,0.6899994804951015,0.6863854617563873,0.6788753489193822,0.6913297346691438,0.6863212332567747,0.6871522219615008,0.7061625317825639,0.7273305124024828,0.7259785220267003,0.7173454472080518,0.7286867770530983,0.726857510848171,0.7279466495594857,0.7309364027598539,0.7251116329695338,0.7107514067434265,0.7035832374433242,0.707648630615574,0.7010017085725602,0.7104250161450182,0.6932341867013152,0.7030113246856673,0.6808684045968002,0.6730064774699848,0.6853962350662837,0.7030353118768232,0.6729848805147445,0.6669488615287438,0.6780267051908526,0.6678329853549753,0.6566474363000729,0.6500059254786901,0.6273250806488695,0.6263352349404837,0.6116368317191008,0.5994779813421177,0.6062230119231778,0.6371922971042665,0.6366244811530632,0.6547949362762162,0.6688649738557746,0.6631158579618,0.6479575321166626,0.6398451818039594,0.6281114473640488,0.6238889351814668,0.6229827194004287,0.608147670608207,0.5991279470481746,0.5903307504617563,0.6134357236490713,0.6252375328283061,0.6479846080335381,0.6418492558104367,0.6381783293663111,0.6042444674197185,0.5825216450151028,0.5750737953632059,0.5798762781023494,0.5865192886519504,0.5801934819425014,0.6001515362629855,0.6033333904124245,0.594608329975963,0.5710125120464777,0.5733932634885126,0.5563435913280241,0.5362372652922621,0.5714744735745276,0.5953563264740044,0.6002137804298339,0.6092587762918386,0.6087220352122464,0.5648024345036885,0.5515354986877418,0.5371070251276702,0.5195030858313976,0.5225967031648171,0.5318281544048883,0.5134322632266143,0.5281397159670282,0.5287201864030996,0.520690480800866,0.5129154873230354,0.531111588374957,0.5107318620038919,0.5253208449716393,0.5400029840424085,0.5431269333588907,0.5340264388618314,0.5511040734967707,0.5367227198085674,0.5309006925213762,0.5375537907486058,0.5175156518371258,0.5002969721196994,0.4979288793105138,0.48011068839534554,0.44533174772855233,0.4511378716806179,0.44647003763354276,0.43941867190211104,0.4589469873834691,0.49558878017464136,0.5150733992977876,0.524629954036891,0.5406881936851304,0.5325124751111001,0.5126566662715244,0.5000982493962688,0.4987907273298028,0.48173684355479046,0.4847822932414253,0.49955583026318545,0.48806679710903184,0.4891107125275533,0.4727522979047511,0.48408349060089967,0.4568364657459676,0.453964972902,0.44130905367866713,0.44134289382215497,0.4205301993426855,0.4421021473821568,0.4412149103397261,0.438408515152729,0.4650890132945307,0.4710124111314907,0.4574766456240608,0.46903123787364337,0.47888900000641205,0.4471903640089037,0.4292193641396417,0.4362952669656656,0.4460418596461384,0.43754123563179803,0.44835343246289094,0.44313090700693464,0.4347409602354541,0.4334590796135652,0.42227090098525333,0.43257877581677934,0.44285000365220695,0.45279429170048074,null,null],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,0.630354587400564,0.6247644986971339,0.6384052148877126,0.645692584073711,0.632080760989416,0.6429080007376513,0.6623502944775465,0.6632430914042315,0.673063684134358,0.6865256202639218,0.6819840877125929,0.6793595022633125,0.6995848202333722,0.6840637246043306,0.7075356873077975,0.7238664435323579,0.7294845215982452,0.7145151968754831,0.7359766461831787,0.7225464082608241,0.7279365414883361,0.7182346676872357,0.733573644941351,0.7252289743271884,0.7408209151523764,0.7343499272337359,0.7537677877832472,0.7466583070950783,0.7690760684516966,0.7752697606358704,0.7788206778060862,0.7965687687705942,0.8064594185777938,0.8235880939960841,0.8101414290844314,0.8081709781978053,0.7785068916039747,0.787560259127078,0.765731464639273,0.7853896960129004,0.7879485871373689,0.8051768668796061,0.8063978234549216,0.8043918904316951,0.788871749117254,0.7834209121250926,0.7826303173540691,0.7664818941765825,0.7699265606863325,0.7908957867476942,0.8210316785271786,0.8203777607090856,0.8595571154396285,0.8726473760626494,0.872346020702232,0.8517861439668646,0.8488166683046293,0.8223169673268153,0.828174379188938,0.8228341124537923,0.8328878020452418,0.8372521115804783,0.8344150596084206,0.819870985613966,0.8068575482247209,0.8039883970622876,0.8062133816102742,0.8088150616159743,0.8076828085942003,0.8256325218491117,0.817810821931651,0.8271497846824463,0.8413599029588271,0.8427835345751724,0.8288710938633301,0.8390663403183949,0.8456733259114998,0.8271559953322424,0.8531322577959349,0.8590238740675302,0.8588583345632586,0.8671003845309702,0.8458928277916792,0.8191146384285561,0.809552580864875,0.8310782199575215,0.8088317843591725,0.8299779714111386,0.8431845380722811,0.8500553328480785,0.8219537380980334,0.8292679229114002,0.838780729489556,0.8152261626683888,0.812503349762523,0.8039668732293975,0.8021966479618848,0.8047185768650769,0.84174089963087,0.8459314659653161,0.8769386872630578,0.8829446176179504,0.8839844165549579,0.849679257859861,0.85185806930245,0.8432915726614592,0.83941620272199,0.8325145320553178,0.8583826369573986,0.8513397536603877,0.8404695247933874,0.8230266253356906,0.8047449240841076,0.782605229623808,0.7876330037679271,0.7729803150046778,0.7801625833955059,0.7789362632542406,0.7900423084303039,0.7824465254947921,0.7767548787344067,0.7770755884190501,0.804277423437241,0.7903581816461498,0.788645116256945,0.7815607939731732,0.7929343858416615,0.7927040022570823,0.7974940889064877,0.7866333101572016,0.8167639970788333,0.8049115324436414,0.7774829560188039,0.7780478238592373,0.7858507826370112,0.7622916214809065,0.7451065795608033,0.73747773688453,0.7272119205015295,0.7195675451782655,0.7197201785560357,0.7296785583715077,0.7395001958629759,0.733138244344351,0.7398442888489782,0.7135604063270512,0.7129256965501344,0.7139430612862337,0.7182117424660465,0.7076031495182351,0.7386766163622644,0.7353020090216562,0.7162684028571944,0.7148393802428187,0.7147299588463888,0.7406400852702737,0.7348876222749468,0.7571626809871316,0.7462334706071272,0.7348950579851912,0.7042865782932258,0.7146769638234113,0.6905985558288181,0.7015557282863739,0.6984693616062271,0.6958440748064856,0.6832338345108686,0.6938282409570761,0.6944487103525177,0.6984287001887821,0.7016356410674878,0.6911881846239769,0.6726581083781695,0.659464654562987,0.6596519019573877,0.6360258928252274,0.6389465285678042,0.6547557906170394,0.6379539864616108,0.6357016408791422,0.6360098281329796,0.6325072456141183,0.6170943070437934,0.6276998140931473,0.6263409560044527,0.639706324589904,0.6296225378622204,0.6302700941450083,0.6329547710124379,0.6348787695578287,0.6168349213418403,0.6350627191202507,0.6327634870793173,0.6253311481096607,0.619163698260704,0.6246928544500998,0.6022760506513043,0.6132843606883812,0.602189543329614,0.6017744907574915,0.6046655972318943,0.6169731442609725,0.6265027040908947,0.6295670629152117,0.6468212266209562,0.6321537649907515,0.6238594532884177,0.6066992362086021,0.6195038340425139,0.5963566972893792,0.6248315867955251,0.6070154380039033,0.6004651849803073,0.6032798903250043,0.6034781374335656,0.5780693856194172,0.619615215264764,0.6338967648988371,0.6329866501420359,0.6267180660803753,0.6316372623303469,0.6120822993074514,0.6105346316237561,0.6064708278912416,0.6148370054968277,0.6237790188498584,0.6152404533685667,0.6137579954763062,0.6168834715705005,0.6205655779695913,0.6036348540100613,0.618852059153614,0.6318428864277708,0.6249432936325613,0.6335233214909725,0.624434408359648,0.6269883820046833,0.6176347370497638,null,null],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM PCR\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003ePCR: %{y:.3f}\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM PCR","x":["2024-08-05","2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,0.8447522421992886,0.8489413892205395,0.8443432341832577,0.84976412926177,0.8559970342206886,0.8520867672521751,0.8518110156270376,0.8443264745605115,0.8460141377884189,0.8773236749832296,0.8957823704356113,0.9124589888286782,0.9545312366467478,0.9614182908125721,0.9506469705836803,0.9589288026639476,0.9572698718068601,0.942940420272679,0.9347629035268744,0.9290999445001372,0.9059465067668766,0.9002090480830904,0.8976041265088843,0.897031146649895,0.8980226118565146,0.9148385433690654,0.9249243865112267,0.9337777623229044,0.9504994460663454,0.9442898825365728,0.9441318960979492,0.9583165980604076,0.9566110262466248,0.9434453466407768,0.9764023654445412,0.977451453525263,0.9662426061461169,0.9725479804842724,0.9991772052814525,1.0064655486696839,1.02239859991324,1.025094845604467,1.0239978308314952,1.019062822149318,0.9944039721396731,0.9718188293548424,0.9659325641646973,0.9678842910610644,0.9677603251794291,0.994026598264182,1.0221942767031091,1.031285542996848,1.0254289146076503,1.0419110477698985,1.022518773776724,1.0048974888194548,1.0086135566364873,1.0330647666644577,1.0294998236761006,1.023053842997782,1.0330925115383802,1.0326232616668143,1.0110367532887854,0.9986458518046997,1.0105158285858622,1.0009499643183117,0.9939753058283592,1.009111263682153,1.0069765878807166,1.0231798849212943,1.0430825217583564,1.0593536131306696,1.0469219199945106,1.049636150844103,1.0197748821402237,1.0199233976175819,1.0070619291396317,1.0014359828078998,1.0089960397680486,1.0253471857679244,1.0094220197942358,1.0103789372733867,1.0152700800319434,1.0154557404858984,1.005980329056841,1.0154510171835518,1.025677613062684,1.0431569914619865,1.032261625546366,1.063965199591625,1.0520191925668148,1.0517246795659514,1.043683471941939,1.0512920201695823,1.0175494553333722,1.0248807729513,1.030143400434649,1.0267254507074723,1.0364670553261164,1.0570131841111017,1.0595179048389995,1.0626936484749616,1.054707525926521,1.0441868487918586,1.027865300581322,1.0183511421043865,1.0041788111483956,1.0327234108463583,1.036725645960733,1.0356328323616908,1.0250854058936771,1.0315382706422462,0.9974130204170665,0.9853042904420821,0.9917532682398524,0.9968491051051679,1.0032587868600529,1.0095600366955764,0.9981252285202522,0.991664190432593,0.9965513406123201,0.9529910606269609,0.9645827675695748,0.9814161374563316,1.0026321278653028,1.011296022493702,1.046658672695581,1.0328470925633926,1.0267552012363859,0.9978298291972745,0.9763367809785842,0.9587904584720555,0.9568385835115489,0.9586546198148115,0.96248367339168,0.9676921107915236,0.9644240901105865,0.9702755525477602,0.9799245536852957,0.9643343340515393,0.9379323668070917,0.9294340280231719,0.9112448343487755,0.898068473903094,0.9054143885021,0.9163094530190827,0.9242554536646553,0.9212345358175206,0.9007662666874902,0.9055031335991804,0.9129732281042358,0.9238272384569116,0.9270736634916708,0.9239031278806138,0.914574694381678,0.8963907203944794,0.8721393937557096,0.8625866496904975,0.8713547705418451,0.8594146549087294,0.8672525972163732,0.8883838429277938,0.8956778671560711,0.9141596900848296,0.9446376627762328,0.9398790851540146,0.9126243733719036,0.90078754031504,0.8767612888621967,0.8638560966364413,0.8918873212519983,0.9048410656272168,0.9197437159895994,0.9166851938720061,0.9055683000021253,0.8803922997548972,0.8774337482163818,0.8687677355089247,0.861727724339933,0.8582153616411455,0.8523897847097028,0.8669329776185144,0.8624789490990838,0.8439319668074698,0.8595289007142087,0.8484265624018011,0.8280046307482862,0.841403641356855,0.8462961689397694,0.8193236346909811,0.8154376882466148,0.8287229384397834,0.8051717932747131,0.8237512288890908,0.8373370671763947,0.8382039035371218,0.8291373278632385,0.8273801592171155,0.8021561979021608,0.8097332128441013,0.8195457769883973,0.8242127022467365,0.8339714678951153,0.8492037632740322,0.8235551188544645,0.81451949432823,0.7963304457082075,0.7775537354900901,0.8025327302556573,0.7951784545842161,0.8086081259431378,0.8196569628646342,0.8336071510180268,0.8052279936576158,0.7948403143371019,0.7994660223769009,0.7947378136037122,0.8041046586931186,0.8081951583055045,0.8269900306557242,0.8269164226555047,0.8147010806425106,0.8093656410750096,0.799337465300628,0.7926822607133396,0.7859453368009625,0.8090954602568867,0.8117898948102601,0.8060128850645112,0.8339659229677767,0.8336063009309733,0.8160986921668784,0.7952125784918402,0.8184483005695334,0.8073569818450956,0.8033833968979239,0.8083780718155911,0.8275003498270485,null,null],"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"PCR=1.0 (看涨看跌平衡)","x":1,"xanchor":"right","xref":"x domain","y":1.0,"yanchor":"bottom","yref":"y"},{"showarrow":false,"text":"PCR=0.7 (偏乐观)","x":1,"xanchor":"right","xref":"x domain","y":0.7,"yanchor":"bottom","yref":"y"},{"showarrow":false,"text":"PCR=1.3 (偏悲观)","x":1,"xanchor":"right","xref":"x domain","y":1.3,"yanchor":"bottom","yref":"y"}],"height":400,"hovermode":"x unified","legend":{"x":0,"y":1},"shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":1.0,"y1":1.0,"yref":"y"},{"line":{"color":"green","dash":"dot"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":0.7,"y1":0.7,"yref":"y"},{"line":{"color":"red","dash":"dot"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":1.3,"y1":1.3,"yref":"y"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"PCR (Put\u002fCall Ratio) 走势图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"PCR比值"}}},                        {"responsive": true}                    )                };                            </script>        </div>
        </div>
    </div>
    
    <!-- 波动率分析文字 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>🌊 波动率深度分析</p>
<h3>📈 隐含波动率 vs 历史波动率
通过IV-HV对比分析，我们可以观察到：</li>
<li><strong>期权定价效率</strong>: IV高于HV时，期权可能被高估</li>
<li><strong>市场情绪指标</strong>: IV-HV价差反映投资者对未来波动的预期</li>
<li><strong>交易机会识别</strong>: 价差异常时可能存在套利机会</p>
<h3>😊 波动率微笑现象
波动率微笑曲线揭示了期权市场的重要特征：</li>
<li><strong>深度虚值期权</strong>: 通常具有较高的隐含波动率</li>
<li><strong>平值期权</strong>: 隐含波动率相对较低</li>
<li><strong>微笑形状</strong>: 反映市场对极端事件的担忧</p>
<h3>🕐 期限结构分析
不同到期日的隐含波动率分布：</li>
<li><strong>短期期权</strong>: 受即期事件影响较大</li>
<li><strong>长期期权</strong>: 反映长期市场预期</li>
<li><strong>期限升水</strong>: 通常长期波动率高于短期</p>
<h3>🌊 3D波动率曲面
三维波动率曲面综合展示：</li>
<li><strong>执行价格维度</strong>: 不同行权价的波动率差异</li>
<li><strong>到期时间维度</strong>: 期限结构的完整展现</li>
<li><strong>波动率水平</strong>: 整体市场波动率环境
</p>
        </div>
    </div>
    
    <!-- IV-HV分析图表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 IV-HV走势对比图</div>
        <div>                            <div id="3c420c5b-a51b-4e6f-aab4-7a23935b9c7f" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("3c420c5b-a51b-4e6f-aab4-7a23935b9c7f")) {                    Plotly.newPlot(                        "3c420c5b-a51b-4e6f-aab4-7a23935b9c7f",                        [{"line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH HV30","showlegend":true,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,10.117539656373594,10.162724097479815,10.159476309495405,10.233967721375778,18.986199424936306,19.190767659741912,25.312930070011248,28.851125114831582,35.61988810191982,35.73484378109262,41.805210957056225,42.68519605517973,43.38160342170133,43.38407553889804,44.2119311379351,44.20716005009968,44.60884158464146,45.29424054183452,45.20888330970542,45.190986473018576,44.7396944201884,44.882247663447814,44.70671784060979,44.700697404104304,44.746478105523884,44.75663846159532,44.756472487291944,44.62063628410502,44.514238972239006,44.57661804016603,44.83753837247442,45.77217476313207,46.25118782156303,46.4441411882096,44.751765497070565,44.72807193540138,41.92954798165685,39.487174799728685,32.16073336981942,32.16395723955488,25.333196786433597,23.021528609048048,23.886571378134416,23.679071659008898,22.752820594160607,23.0447813453413,22.58198950247652,20.203307770044347,20.281435581362008,20.246518522355295,20.188812921172808,20.015185806185652,20.29236879142999,20.285093525734588,20.368368347065687,20.054857714230202,20.315163472003427,21.71295277585042,21.52311594518396,20.680401179365504,20.669828648041726,16.930013272524928,16.378441050456324,16.51403812593633,16.257838282227368,16.146134873597052,15.973507202970971,15.46495324574019,15.572386720188772,16.28723334997132,17.588367312339127,17.768686193622273,15.365423025009914,15.305124255522202,15.249683091193003,14.929216070097356,14.99290823542859,14.921411379775487,15.63567690610837,15.57948989258067,15.636249262164078,15.639927624431271,15.293327921819863,15.278842078974508,15.400762725445972,15.548386963587157,15.321731281943382,13.500443540921934,13.535622196370564,13.750926951550117,14.138214678132066,14.156748923003432,14.15469551654564,14.322031236248453,13.879831475287608,14.038356927376187,14.036817422870062,14.086444323768813,13.973010864678287,13.229567195699424,11.983864894991116,11.673842261070707,12.052218069750937,12.165554817409504,12.257372661383659,12.886071924151743,12.859777450359367,12.89274586761826,12.15822489124161,13.162136656727402,13.032877123577785,13.214559868578943,13.23425960137261,13.244455635087451,12.520859451649425,15.162463257987524,15.14690557320929,14.847794860072003,14.728322632536564,15.022596846863417,15.707637724500964,15.776333974090232,15.723557268738212,15.542707246349034,15.608300654089385,15.545572786244158,15.599639470687634,15.544894623705888,15.500880536777128,15.42443260590655,27.833710180422244,29.37375332584844,29.937955770279125,29.907820982452794,29.88791510078422,29.644225899434478,29.580387021143785,29.59273706470662,29.571055810728588,29.070654748574448,29.069832870387003,29.025703263331216,29.01545885301553,29.030114432204268,29.03462997988196,27.456773983017012,27.451285988778345,27.458239779210558,27.552319866440644,27.499979096686726,27.080977851982457,27.014102664423888,27.108260423018226,27.061629367470353,27.530655670384434,27.539839630423874,27.68567228701523,27.691887229701923,27.69812590395948,27.72484054562997,12.607566205653983,10.42413830137386,9.036816680137843,9.130862043733831,9.070595971019676,9.039655840696652,9.088788016593986,9.026686991675895,8.931692603612323,8.930628762340104,8.924751530804215,8.911495843939859,9.038995587610081,9.30112641928056,9.242571011626568,9.464000213936194,9.503461461319153,9.368898882020748,9.155173970671079,9.23012916254249,9.36548376655231,9.578209146711496,9.860949347494428,10.798394552183673,9.439756621901847,9.877496754207746,9.495322198522812,9.47025872885963,9.430031554545556,9.333364120617988,9.480163353611173,9.211656988164297,9.376917358181174,9.197590407547546,9.305347124745165,9.330065659613835,9.306201612689314,9.457036881720752,9.47293561124121,9.519306250992786,9.825211251926728,9.825735105129668,9.837160969674848,9.630076712849537,9.6687337569534,9.720324511844169,9.681658483177793,9.687247702005033,9.648725503755644,10.566391895727932,10.49925165308069,10.468177288223583],"yaxis":"y","type":"scatter"},{"line":{"color":"#ff7f0e","dash":"dash","width":2},"mode":"lines","name":"IH IV","showlegend":true,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,13.778498658427964,13.582240016119735,13.383161324866581,13.183251713144841,12.984508611713505,12.788917795946338,12.598433544620761,12.414959113410294,12.240327718183014,12.076284218114562,11.924467681632052,11.786395009384144,11.663445777871178,11.556848455172826,11.467668126501088,11.396795852220968,11.34493976466981,11.312617992733074,11.3001534848718,11.307670782328323,11.335094774751333,11.382151450673724,11.448370635344538,11.533090688559696,11.6354651155522,11.754471024888026,11.888919348859218,12.03746672425536,12.198628914804722,12.370795641172485,12.552246670339528,12.741169003602145,12.935674991455812,13.133821194365005,13.333627800968703,13.533098409700987,13.730239976175632,13.92308272702676,14.109699841233198,14.288226702277218,14.456879528776401,14.613973197437579,14.757938080251753,14.887335727698307,15.00087324125713,15.097416191623255,15.175999953549479,15.235839344062974,15.276336467754001,15.297086690749211,15.297882683679545,15.278716493246765,15.239779621690174,15.181461113359546,15.104343667512588,15.009197816176561,14.896974225246982,14.768794195748228,14.625938460164365,14.469834385783525,14.30204171291577,14.124236970483514,13.938196724698706,13.745779828200492,13.548908847014124,13.349550850906716,13.149697759076382,12.951346437554038,12.756478747178113,12.567041741496144,12.384928212449358,12.211957778221262,12.049858702214234,11.900250624812912,11.764628380472836,11.644347061828693,11.540608480056662,11.454449156774386,11.386729967459624,11.338127539867017,11.309127493387246,11.300019586898593,11.310894823592022,11.341644541697367,11.391961500195823,11.46134294867065,11.549095650623144,11.654342810062586,11.776032832161937,11.912949830445756,12.063725775525935,12.22685416399913,12.400705070930801,12.583541435526346,12.7735364172684,12.968791649103382,13.1673562052976,13.367246094442278,13.56646408283989,13.763019650203079,13.954948878275387,14.140334073653282,14.317322928744751,14.48414702941445,14.639139524393205,14.780751779904898,14.907568853103243,15.018323629712995,15.111909484616927,15.18739133888821,15.244015002789952,15.28121471138974,15.298618777495836,15.296053305432725,15.273543928549227,15.231315553098556,15.169790111049366,15.089582344281007,14.991493662285867,14.876504134750633,14.745762699023953,14.600575680314234,14.442393739319975,14.272797377707597,14.093481146261224,13.906236713491406,13.712934963875593,13.515507304598884,13.315926367571873,13.116186299544637,12.918282837251622,12.72419336666987,12.535857165631981,12.35515602720307,12.183895457426441,12.023786635304102,11.876429315261756,11.743295842931403,11.625716443960506,11.524865932836992,11.441751974531261,11.377205016240886,11.331869989836715,11.30619986791681,11.30045113785398,11.314681239058736,11.348747989063686,11.402311004163753,11.474835100417632,11.565595641028839,11.673685776677027,11.798025506456648,11.937372468889,12.090334355187432,12.255382820746538,12.430868755856213,12.615038763060776,12.80605267652676,13.002001948371603,13.200928718243265,13.40084537561363,13.599754419325905,13.79566841596592,13.986629857639798,14.170730720745787,14.3461315303154,14.511079739439204,14.663927240136271,14.803146830704302,14.927347475014212,15.03528820128334,15.125890501455256,15.19824910729579,15.251641035533954,15.285532811671814,15.299585800285339,15.293659588557599,15.267813389237233,15.222305449004232,15.157590468154481,15.0743150573847,14.973311277072114,14.855588323602186,14.722322445811962,14.57484519230048,14.414630107035318,14.243278006188394,14.062500983309882,13.874105302655451,13.679973351590876,13.48204483239969,13.282297381419193,13.082726809151842,12.885327158786476,12.692070782377908,12.504888633757128,12.325650975078982,12.156148689780874,11.998075388667502,11.853010487911511,11.722403428049164,11.60755919164966,11.509625264360636,11.42958016961092,11.36822369152786,11.326168883758703,11.303835944041207,11.301448015726745,11.319028958205688,11.356403108512275,11.413197036490892,11.488843275986753],"yaxis":"y","type":"scatter"},{"line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IF HV30","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x2","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,10.162334452931285,10.5811278358799,10.580779174466235,10.656982837704517,17.72076950890745,18.11201265397189,23.75588361211666,29.958454649864237,38.21251737697481,38.40693080216699,45.673589590386044,45.63638071819605,46.58287484694316,46.573281824322606,47.636758826470604,47.70946783954777,47.89570740531862,48.78447831907893,48.73894946945912,48.68345385165502,48.1041323118619,48.38943328436765,48.26073487147727,48.240942181976806,48.23224042047569,48.21811213307479,48.224421413843245,48.16940683869826,48.112397039731235,48.2750566302136,48.47106193296799,49.09097311410625,49.599544788678266,49.59216571738476,48.54493616038945,48.50756998963629,46.8584204143159,43.29767085821498,34.949751277206545,34.15535258227742,25.793886954425595,25.166489695419397,25.730757411523914,25.35744762299168,24.024585570976257,24.468926015510313,24.278215800716616,21.529729191777882,21.648132429881365,21.559391090476083,21.581331977466924,21.328008806351015,21.590641534555335,21.569232781298115,21.590919200430267,21.314223099769855,21.514054121041347,22.882402819323257,22.58759717800579,21.02225966489196,21.016140827515944,18.030113493679544,17.44505075028818,17.34512575846779,17.555888691882735,17.398971114342686,16.704204266241046,15.993873911011919,15.951515100199654,17.014780591451018,18.58209418719803,18.72149970509131,16.381583062973597,16.480495064232795,16.48878589979331,15.639019424479422,15.83839724871072,15.480037771509872,17.08739219226389,17.132519275388784,17.090441887422802,17.151808180375205,16.775043851451255,16.788897644933495,16.71124649319123,16.74746446215385,16.793999946843044,15.548622886991255,15.481951326145388,16.046278024724106,16.5869080391265,16.582135163937263,16.60860105834897,16.833185937969773,16.37058001160434,16.596417000981027,16.596807824437583,16.730892018922944,16.766340186039745,15.67706469087812,14.040093931870295,13.693960263287874,13.979261038395366,13.977211838049767,13.952552258647577,15.104971670355264,14.674951834076857,14.675838557316142,12.897053797619131,13.411402557358226,13.45026353598324,13.618551120592596,13.575856865729726,13.597166809500601,13.265978634796216,15.350814222156076,15.144631132120137,14.549272239434725,14.511909465302189,14.381223765537024,15.534394597320933,15.590493494421779,15.543823715377627,15.343540091313649,15.363956642968185,15.204207356518417,15.29796296893583,15.159847484204978,15.057440003225,15.101594972872991,32.110972794554534,33.34141302780536,33.95762624704091,34.284693042242196,34.27777963189743,33.9460605361521,33.94180143682864,33.94485140385545,33.97648063889709,33.609000967831115,33.69761133512667,33.66667927968816,33.64375954006215,33.649805459593466,33.63988964208161,32.506953379765015,32.50202192755551,32.47365366910463,32.774296347807976,32.76426762179138,32.17119069160961,32.14089303913612,32.33407168392225,32.321778969249394,32.592901416358565,32.66196441211263,32.66888981426154,32.669744075269065,32.7107342059563,32.683983676355396,12.526294228674228,11.092792255608675,10.000190796940359,9.265025027147747,9.258293398581326,9.428904256939893,9.473236355206563,9.469281267704527,9.442707606892958,9.436549109781376,9.302088934412936,9.315400310042907,9.60692578836279,9.945050272251756,9.943318531386812,10.181209599398935,10.170483207256604,10.12536085290071,9.453777750128284,9.732811849691231,10.748403417320851,10.9070108146252,11.198024836601972,12.38171532518336,11.734881101179065,11.628736642832479,11.454732416134423,11.450476117994363,11.349589022746487,11.423637125743777,11.486979776298083,11.266102633980342,11.521988687790868,11.366376861703905,11.390000592881531,11.31774251780485,11.294603680905297,11.320250261102824,11.328027271869594,11.630255378088416,11.761997496267874,11.813262029041983,11.824744241810308,11.622655355680116,11.74212301915419,11.682629230328303,11.678131275545006,11.677448285929943,11.715612291903549,12.93515456241307,11.883545339567233,11.868168037114577],"yaxis":"y2","type":"scatter"},{"line":{"color":"#ff7f0e","dash":"dash","width":2},"mode":"lines","name":"IF IV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x2","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,13.778498658427964,13.582240016119735,13.383161324866581,13.183251713144841,12.984508611713505,12.788917795946338,12.598433544620761,12.414959113410294,12.240327718183014,12.076284218114562,11.924467681632052,11.786395009384144,11.663445777871182,11.556848455172819,11.467668126501096,11.396795852220961,11.344939764669803,11.312617992733067,11.3001534848718,11.307670782328316,11.335094774751333,11.382151450673724,11.448370635344531,11.533090688559689,11.635465115552194,11.754471024888026,11.888919348859211,12.03746672425536,12.198628914804722,12.370795641172485,12.552246670339535,12.741169003602145,12.935674991455812,13.133821194365005,13.333627800968706,13.53309840970099,13.730239976175625,13.92308272702676,14.109699841233198,14.288226702277218,14.456879528776401,14.613973197437579,14.757938080251753,14.887335727698307,15.00087324125713,15.097416191623255,15.175999953549479,15.235839344062974,15.276336467754001,15.297086690749211,15.297882683679545,15.278716493246765,15.239779621690174,15.181461113359546,15.104343667512588,15.009197816176561,14.896974225246982,14.768794195748228,14.625938460164365,14.469834385783525,14.30204171291577,14.124236970483514,13.938196724698706,13.745779828200492,13.548908847014124,13.349550850906716,13.149697759076382,12.951346437554038,12.756478747178113,12.567041741496144,12.384928212449358,12.211957778221262,12.049858702214234,11.900250624812912,11.764628380472836,11.644347061828693,11.540608480056662,11.454449156774386,11.386729967459624,11.338127539867017,11.309127493387246,11.300019586898593,11.310894823592022,11.341644541697367,11.391961500195823,11.46134294867065,11.549095650623144,11.654342810062584,11.776032832161937,11.912949830445756,12.063725775525935,12.226854163999132,12.400705070930801,12.583541435526346,12.7735364172684,12.968791649103382,13.1673562052976,13.367246094442278,13.56646408283989,13.763019650203079,13.954948878275387,14.140334073653282,14.317322928744751,14.48414702941445,14.639139524393205,14.780751779904898,14.907568853103243,15.018323629712995,15.111909484616927,15.18739133888821,15.244015002789952,15.281214711389742,15.298618777495836,15.296053305432723,15.273543928549227,15.231315553098556,15.169790111049366,15.089582344281007,14.991493662285867,14.876504134750633,14.745762699023953,14.600575680314234,14.442393739319975,14.272797377707597,14.093481146261224,13.906236713491406,13.712934963875593,13.515507304598886,13.315926367571873,13.116186299544637,12.91828283725162,12.72419336666987,12.535857165631981,12.35515602720307,12.183895457426441,12.023786635304099,11.876429315261756,11.743295842931403,11.625716443960506,11.524865932836992,11.441751974531261,11.377205016240886,11.331869989836715,11.30619986791681,11.30045113785398,11.314681239058736,11.348747989063686,11.402311004163753,11.474835100417632,11.565595641028839,11.67368577667703,11.79802550645665,11.937372468889002,12.090334355187434,12.255382820746537,12.430868755856213,12.615038763060774,12.806052676526757,13.002001948371603,13.200928718243265,13.40084537561363,13.599754419325905,13.79566841596592,13.986629857639798,14.170730720745787,14.3461315303154,14.511079739439204,14.663927240136271,14.803146830704302,14.927347475014212,15.03528820128334,15.125890501455256,15.19824910729579,15.251641035533954,15.285532811671814,15.299585800285339,15.293659588557599,15.267813389237233,15.222305449004232,15.157590468154481,15.0743150573847,14.973311277072114,14.855588323602186,14.722322445811962,14.57484519230048,14.414630107035318,14.243278006188394,14.062500983309882,13.874105302655451,13.679973351590876,13.482044832399689,13.282297381419193,13.082726809151842,12.885327158786476,12.692070782377908,12.504888633757128,12.325650975078982,12.156148689780872,11.998075388667502,11.853010487911511,11.722403428049164,11.60755919164966,11.509625264360636,11.42958016961092,11.36822369152786,11.326168883758703,11.303835944041207,11.301448015726745,11.319028958205688,11.356403108512275,11.413197036490892,11.488843275986753],"yaxis":"y2","type":"scatter"},{"line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IM HV30","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x3","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,15.781595666282769,16.352147605701145,16.764196489794976,16.760408026087905,22.132623148956316,22.175701010052652,27.439821112066138,36.455927699684935,42.992400415865035,46.33361550130545,52.607995082472584,52.459838146854,54.38843268018738,54.733373741724556,55.45869641119846,55.44499630304786,55.34997776009579,56.27002358483802,56.259872987651704,56.162105339427896,55.408663401497606,55.58037982486789,55.66845131459106,55.66860614304977,55.582505037576404,55.328406763257135,55.30469243722189,56.40463710682428,56.46197352362281,56.5690261161511,56.49299974908018,56.49869827700592,56.078253315925274,56.01451019169368,55.471263374124824,55.569668420264364,54.83113981878465,50.28638791018224,44.962495095228846,40.58943661837613,33.98855540912107,33.59475329663071,33.09427943913407,31.728187821266964,31.13476774109048,32.036969645572746,32.0806738774684,30.198322484293005,30.26079884961818,30.26445290843255,30.6813830032576,30.773348243329323,30.168483116848705,30.28278505710238,30.103271258122916,30.21197342077027,30.027437591638744,29.24187875418672,28.90869867673459,27.396281886022972,27.491710500333934,26.907170683517716,27.116147825515156,27.41222589893941,27.512311805713967,27.58218155163572,26.877535686715685,26.138837096783305,25.110673407881066,26.15851440974664,26.54299584642603,27.355479981921775,25.31094540396545,25.561584879491186,25.37729001809323,23.608780723498388,24.018410507883736,23.14156199893982,27.030096987456105,27.059005339663067,26.87244402975391,26.710020153710868,26.478625905259367,26.449729073984834,25.96651265256534,25.619971312937988,26.740598435372213,26.936314632466658,26.84171668421815,27.621110018667604,28.04648943689555,28.114299241914782,27.884417208326184,27.13080245919906,27.096337710549534,26.973722212380007,26.88853201248052,27.416070186215148,28.064731434682454,25.946804250492093,25.22461064127531,23.79112184761715,23.704938762157678,23.63641378465787,23.481879200937147,25.390120564472436,24.53644513410692,24.59394515816031,20.762666728623483,21.48064821584291,21.600333386268954,21.646114464380716,21.701863503165498,21.698521911542965,22.067946357355765,22.4927955234013,21.718523970038532,20.35316942429937,20.478366611484645,19.3650181588045,25.07618115246465,25.038208386232846,25.067924401374004,24.69467881439287,24.606757324570065,24.744805802557313,24.759825736006295,24.25998553399423,23.480067585751435,23.65724592693442,36.11011696060602,36.4338243461684,37.28935071865458,38.21315592655603,38.69716844724167,38.013513180437535,38.01369048019802,37.93584186296851,38.02573505053084,37.01245941229988,37.831299213105154,37.84344093492831,37.814471363704875,37.78919540408228,37.727589309433625,37.04571084355065,37.208591772869525,37.201966998365045,38.208124968040494,38.21939428438512,35.084289097812864,35.21838113789335,35.51207428442615,35.55344685034712,35.6253042886636,35.778960538830475,35.76081499738599,35.81619741640184,35.84043121036755,35.701459162777674,19.949719067083034,19.13758643165764,18.41245030163575,16.934499228666475,16.38487208703078,17.474928083334287,17.826377973660485,17.248208501291376,17.257542327550404,17.276707495437364,16.213559435104667,16.416286061698695,16.60014240610839,16.30651104009162,16.302757032327417,16.429243011358928,16.40969532955881,16.394414112956255,14.701493037264754,15.179611471433367,19.522120073820254,19.54339437462359,20.351762595699544,21.1277411042787,21.057319260120025,20.510610080927467,20.5362737353397,20.489685953156535,20.423144935387654,20.40349023526462,20.265530627068287,19.82576853437661,20.462918336350764,20.49627959062063,20.45503900869909,19.87060222017287,19.569424709700105,19.612070437478717,19.48242185883728,19.77666118797339,19.776962788418352,19.663122416698638,19.4946435577808,19.520239377521914,20.083564799853153,19.67336478116328,19.654028436710032,19.732294670784363,19.987167663271528,19.866385234375517,14.316365553416691,14.54670348389286],"yaxis":"y3","type":"scatter"},{"line":{"color":"#ff7f0e","dash":"dash","width":2},"mode":"lines","name":"IM IV","showlegend":false,"x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"xaxis":"x3","y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,17.117747987641945,16.8233600241796,16.52474198729987,16.224877569717258,15.926762917570253,15.633376693919503,15.347650316931139,15.072438670115442,14.810491577274519,14.56442632717184,14.336701522448074,14.129592514076215,13.945168666806765,13.785272682759235,13.651502189751632,13.545193778331452,13.467409647004708,13.418926989099603,13.400230227307695,13.411506173492477,13.452642162126999,13.523227176010586,13.6225559530168,13.74963603283954,13.903197673328293,14.081706537332039,14.283379023288823,14.50620008638304,14.747943372207082,15.006193461758727,15.278370005509288,15.561753505403225,15.853512487183714,16.150731791547507,16.45044170145305,16.749647614551478,17.045359964263447,17.33462409054013,17.614549761849794,17.882340053415824,18.1353192931646,18.370959796156367,18.586907120377628,18.78100359154746,18.951309861885694,19.09612428743488,19.213999930324213,19.30375901609446,19.364504701631,19.395630036123812,19.396824025519315,19.368074739870146,19.30966943253526,19.222191670039315,19.10651550126888,18.96379672426484,18.79546133787047,18.603191293622338,18.388907690246544,18.154751578675285,17.903062569373652,17.63635545572527,17.357295087048055,17.068669742300735,16.773363270521184,16.474326276360074,16.174546638614572,15.877019656331054,15.584718120767166,15.300562612244214,15.027392318674035,14.76793666733189,14.524788053321348,14.300375937219368,14.09694257070925,13.916520592743037,13.760912720084988,13.631673735161577,13.530094951189433,13.457191309800525,13.413691240080865,13.400029380347888,13.416342235388031,13.462466812546047,13.537942250293732,13.642014423005971,13.773643475934712,13.931514215093875,14.114049248242903,14.31942474566863,14.5455886632889,14.790281245998694,15.0510576063962,15.325312153289516,15.610304625902597,15.90318747365507,16.201034307946397,16.500869141663415,16.79969612425983,17.094529475304615,17.382423317413078,17.66050111047992,17.925984393117126,18.176220544121673,18.408709286589804,18.621127669857344,18.81135327965486,18.97748544456949,19.117864226925388,19.231087008332313,19.316022504184925,19.371822067084608,19.39792816624375,19.394079958149085,19.360315892823838,19.29697332964783,19.20468516657405,19.084373516421508,18.9372404934288,18.764756202125948,18.568644048535926,18.35086352047135,18.113590608979962,17.859196066561392,17.590221719391835,17.309355070237103,17.019402445813387,16.723260956898326,16.423889551357806,16.124279449316955,15.827424255877432,15.536290050004801,15.25378574844797,14.982734040804601,14.72584318613966,14.485679952956147,14.264643972892628,14.064943764397103,13.888574665940759,13.737298899255485,13.612627961796889,13.515807524361328,13.447804984755066,13.40929980187521,13.400676706780963,13.4220218585881,13.473121983595526,13.553466506245627,13.662252650626446,13.798393461543256,13.960528665015538,14.147038259684969,14.356058703333499,14.585501532781144,14.833074231119802,15.096303133784318,15.37255814459116,15.659079014790137,15.953002922557403,16.251393077364895,16.551268063420444,16.849631628988856,17.143502623948876,17.429944786459696,17.706096081118677,17.969197295473098,18.2166196091588,18.445890860204404,18.65472024605645,18.841021212521316,19.002932301925007,19.13883575218288,19.247373660943683,19.327461553300928,19.37829921750772,19.399378700428006,19.390489382836396,19.351720083855845,19.283458173506347,19.186385702231718,19.061472586077045,18.909966915608166,18.733382485403276,18.53348366871794,18.312267788450715,18.071945160552975,17.81491700928259,17.543751474964818,17.261157953983172,16.96996002738631,16.67306724859953,16.373446072128786,16.074090213727757,15.777990738179712,15.488106173566857,15.20733295063569,14.938476462618471,14.684223034671307,14.44711308300125,14.229515731867263,14.033605142073743,13.861338787474487,13.714437896540954,13.594370254416377,13.502335537291788,13.43925332563805,13.40575391606181,13.402172023590115,13.42854343730853,13.484604662768408,13.569795554736336,13.683264913980128],"yaxis":"y3","type":"scatter"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"IH IV-HV走势","x":0.14444444444444446,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IF IV-HV走势","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IM IV-HV走势","x":0.8555555555555556,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":400,"legend":{"x":0,"y":1},"showlegend":true,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"IV-HV走势对比图 (一年期)"},"xaxis":{"anchor":"y","domain":[0.0,0.2888888888888889]},"yaxis":{"anchor":"x","domain":[0.0,1.0],"title":{"text":"波动率 (%)"}},"xaxis2":{"anchor":"y2","domain":[0.35555555555555557,0.6444444444444445]},"yaxis2":{"anchor":"x2","domain":[0.0,1.0]},"xaxis3":{"anchor":"y3","domain":[0.7111111111111111,1.0]},"yaxis3":{"anchor":"x3","domain":[0.0,1.0]}},                        {"responsive": true}                    )                };                            </script>        </div>
    </div>
    
    <div class="chart-container full-width">
        <div class="chart-title">📊 IV-HV价差图</div>
        <div>                            <div id="5790b47a-0511-4897-b3a4-33285e934008" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("5790b47a-0511-4897-b3a4-33285e934008")) {                    Plotly.newPlot(                        "5790b47a-0511-4897-b3a4-33285e934008",                        [{"fill":"tonexty","hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":2},"mode":"lines","name":"IH IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1.1625616572557416,2.6965782985939004,4.946477494580424,2.0697033151901305,2.978389208766398,1.164717545555657,1.7372765649434712,6.413505771367641,0.5846781523387428,2.9295337422608085,1.5192880568806757,2.6025930663049497,-0.6780038308156549,2.5876482465685395,1.922807095443627,4.780614075596333,1.1214731995786629,5.113479432301659,6.264525337197412,3.184856381748638,2.419806276776697,2.796891529324739,4.560123138551901,1.6953461753643424,5.644486305818859,5.2852552507435675,2.3949218199103584,0.795677268193522,5.925078070771917,-0.7940116270518729,0.2542293830209914,-1.4167844379708896,0.4276876359432009,5.37105131200093,3.6037165438805943,2.600791861527341,3.4402829955748757,5.908090435444791,0.7337431700258179,3.4298994228566215,3.539469751156144,1.4516629855947656,-0.3905104774174504,-2.2943781151719094,-2.115214645176877,0.49231559386956647,4.084972218919038,3.3381525836091974,7.036488922126264,1.0439757959904128,4.637731525352127,-0.8148458746723328,4.691555391162339,2.2635597098325846,5.556291001534362,2.1747171900975246,-5.2811094387959585,4.629973354606847,4.374531176060525,-3.0264143033327713,-0.9470628909697787,3.930322095340852,3.6440492292060185,7.155205660038501,5.883678432057591,2.0081361161200117,-2.792724917805563,1.3905985179305098,1.7054921551012328,2.147574883575718,6.579653997441238,-0.7839682125535674,4.773979369580022,8.33046600082035,0.06921114303404607,0.2311910581000074,4.589546620597446,5.530913806805497,1.2555315620649257,2.40009380245583,-1.8518510798940788,4.277375174249979,0.2136200368097647,2.1810071033944034,1.1914982670791332,4.565465947788812,0.5552858278552968,-0.7763707420347981,1.8606636634356875,5.283562689129944,1.465146798099756,7.459354809545681,3.9869748988713383,1.1349054073088407,1.8142972663003185,3.4713837443633686,5.6786726719455025,8.521446733459582,5.531179615973683,4.7006056877265525,4.383139832816372,5.5646829509493525,-1.883888265219845,4.962337225252686,1.8665395991645877,5.547193289271007,6.628729523084839,3.2223440100426224,-1.663979166003319,-0.47193138658205847,-1.73215859921922,5.823310618335636,2.082207859151298,-3.525047948984236,0.3996965974990321,-2.795798351356833,2.622229933913376,6.504090933014712,11.30607654525961,4.087759635992075,-0.17839964418806886,-1.1229814055174039,-1.6203508430559115,5.062892008483319,2.526110867170244,1.0616046942514323,0.798165389727842,-0.7773433534657386,-1.7922249256446214,1.3643249943367302,2.9654049621881917,-0.8871757754166225,0.26966499121158094,4.784083104695988,0.26605088916422304,3.3979759852085607,-5.0021221121544475,-3.447425038864573,0.5306043871942201,5.247041846050687,1.1721319568376245,5.418021532113876,4.034553681059592,5.311807358645076,9.075182698933858,4.5218862633605,0.9905084604483889,2.551055938365458,3.2190830995385937,2.6584666290015733,4.817244398775614,2.776786303332102,4.460131888335795,1.4144161315830672,4.105472572666912,0.3306252332731745,2.2671188932603883,1.6689472621862436,4.281529860376004,3.023273410050834,1.8710549821249494,5.134952443165945,4.253309143979748,-0.47838459737939676,1.4174466415118658,3.029200503948589,8.179134844939995,5.80718035097224,0.7715685615864345,-0.8321661156125799,6.1197245302047705,2.334385629706677,-1.2251410458189955,-0.1786394689300259,4.807827234184172,4.503160399849801,1.6092029878883523,-0.7920647494287003,-0.0236863293919658,0.7718837969441559,-2.052003544048545,1.3280374927497807,-0.6282258477709828,1.4717328822219198,-1.562040584793003,3.6803380575700952,0.9914591340657992,4.551812478570847,3.164575910427244,1.912675987136761,-1.0191053011020612,2.9336046140678755,-1.5858361717932006,-1.0259934549236593,1.2613563443540787,-3.0523567847107476,9.850827170066873,1.284250540195421,-4.169586652981715,6.053488753487937,3.6873550233501025,3.5129040423815816,2.8466176128000793,-2.09997607825572,4.131162831356383,0.3089294313183455,-1.0544961459255973,0.6529398562554238,0.9565949768201047,3.4003675385763206,6.854988255430632,5.032330350822377,-0.5566528776457851,2.4412934032974434,3.728400550278944,-3.833234661109027,-6.11069486452873,-4.527502953519676,1.452831338616221,1.0423115663532236,1.8027573307148543,3.768834470483192],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":2},"mode":"lines","name":"IF IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1.8709452517065088,1.6837455993272137,4.080145279335655,0.9932997271597888,0.5551151010356872,2.3472926225204063,7.671454738899971,5.022962023716222,2.601862933401513,0.049823046916452896,2.233591650103504,0.7103231336072131,0.12933757575005345,-0.6693180916092754,3.8102766416322993,7.7718354969904375,1.678483296057962,-2.161938780247681,4.743648555994575,3.9527995012886805,0.32128910638888186,-1.4482083128049439,0.507337428404611,2.3079735909581487,1.4283974953872018,2.4312030149497588,2.8737651617772713,2.5243205966283284,0.6564901716801046,0.216517006415458,1.0472822365572654,2.776681849570636,1.8563456299830392,4.021442509488146,6.736348898772732,3.3010604713941376,4.811341816908694,5.542381381459272,-2.974490108117436,-0.9797480414796524,-0.904569872004874,6.492645472506055,4.004248329179582,-1.5395359491295721,-1.5497818076228924,-3.9514914467079265,-3.184420446897235,8.18260833192966,3.2561660872221054,6.196619056819262,-0.8994297225656815,2.8514786142441295,0.7571313485991631,1.5961560215427113,-1.033766066717991,-7.288485659576995,2.7442075618824475,-1.7296252524101448,5.195511919383382,1.3807131321382613,-1.2991951276751657,0.24108194113223647,4.79506589878406,3.2222799169881,-3.970058836016438,5.249883717805769,2.0809403755374447,10.134181456945154,-0.3042120190103681,5.126023985956536,1.8805203565782236,-1.4564056668917509,1.288366558943931,0.8122863856433735,2.4297175182481023,-2.9153702011302904,5.0814269501322915,0.26387657729907055,5.024041659794003,-0.6114444158622074,8.184748447801105,-0.5350859236659886,1.9105196803794975,-2.6592954201291796,0.42624362921088377,2.5521754487114734,4.082729361546921,4.896108885097155,-0.8464776139479895,2.1755288686138172,0.24998873880137396,3.6232041987936334,2.598427643099356,1.476297608897319,6.871095039492136,4.562813535632138,2.881395853842868,0.8324639297219747,-1.1178249764322281,3.486256296175064,-2.584587053106942,3.1484139224260552,6.866934096482421,-1.3660571268798858,5.9076675427120175,-0.4583115710336312,2.4334482482325335,1.0213984456236815,-0.8847338322265195,3.3075470133433686,-1.6058688577178657,4.246788187309811,1.3793634265120307,-1.3131483818358358,0.8455265418022186,2.435031022306614,4.3901852484957615,-0.5489369434211984,4.76717986450573,-2.0229212855064898,0.3399408491245204,-6.032549547407088,5.279462783948242,-3.608361176915297,0.23438602561594557,0.7569626826462361,4.806100741953927,2.3475864754024247,3.6932951553893627,1.608240401136321,0.17437576900303853,5.424988223375383,6.077769236144459,2.079172594084241,2.7462521336660544,2.447866151246613,-0.4258341809783861,4.1245241356959905,6.851469346438382,0.03733807054169702,1.5399453312071785,2.843118400126869,-0.0625712074633924,6.197862994642861,0.018524934860600695,2.9540139103846172,1.592001639533251,2.128529279451058,1.5982431379157518,6.203591582204538,3.7950069464092024,6.483367074577252,6.02162722227208,-1.9510020179752416,-0.13399131458882607,2.7120139757990174,5.57142465041693,1.8629052390128251,5.931606902037885,-0.6001943289071363,0.14914182665841658,1.5846103545632264,2.7080126863360494,3.5409857788444414,0.07978353714310238,6.889489485128122,2.1489960192239685,1.1722641086602863,1.5019511708255155,6.041656920450944,2.8092699567057426,2.251986078521167,-1.635809459625845,-4.416238487377923,0.32604314477879726,0.48773508339352034,0.35099238447698866,0.6035366374316027,-1.974499865958343,3.0812737984869614,-2.288929319757661,2.868127926095628,3.190832309265886,1.1719363212691967,-2.3213425768905633,1.7358175123030044,-0.21655840757731504,2.911040516914319,-0.9853309692152674,7.34991090849342,2.3284215456539705,-0.6979417067908358,0.218708548515135,2.2519024407299195,1.3633954291804002,1.944110275979689,0.8586297377418664,3.6121998733316065,0.3422856686540303,3.074726740324895,3.611683535552645,0.6146052882836841,2.68029781047351,5.923444808700282,4.310953592308518,-0.7326536012930553,1.9079023604212093,7.640299905663756,3.9461223984139,3.301747774182658,-0.4396935618494737,4.679661939373496,-2.257316345097755,0.7172882803388276,-0.6053252519601147,-2.8303629021447687,0.6001460351874677,2.417399263930802,4.268333076577703,1.108575069520855,-0.3725872716139378,-1.9742005122776352],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e日期: %{x}\u003cbr\u003eIV-HV价差: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":2},"mode":"lines","name":"IM IV-HV价差","x":["2024-08-06","2024-08-07","2024-08-08","2024-08-09","2024-08-12","2024-08-13","2024-08-14","2024-08-15","2024-08-16","2024-08-19","2024-08-20","2024-08-21","2024-08-22","2024-08-23","2024-08-26","2024-08-27","2024-08-28","2024-08-29","2024-08-30","2024-09-02","2024-09-03","2024-09-04","2024-09-05","2024-09-06","2024-09-09","2024-09-10","2024-09-11","2024-09-12","2024-09-13","2024-09-18","2024-09-19","2024-09-20","2024-09-23","2024-09-24","2024-09-25","2024-09-26","2024-09-27","2024-09-30","2024-10-08","2024-10-09","2024-10-10","2024-10-11","2024-10-14","2024-10-15","2024-10-16","2024-10-17","2024-10-18","2024-10-21","2024-10-22","2024-10-23","2024-10-24","2024-10-25","2024-10-28","2024-10-29","2024-10-30","2024-10-31","2024-11-01","2024-11-04","2024-11-05","2024-11-06","2024-11-07","2024-11-08","2024-11-11","2024-11-12","2024-11-13","2024-11-14","2024-11-15","2024-11-18","2024-11-19","2024-11-20","2024-11-21","2024-11-22","2024-11-25","2024-11-26","2024-11-27","2024-11-28","2024-11-29","2024-12-02","2024-12-03","2024-12-04","2024-12-05","2024-12-06","2024-12-09","2024-12-10","2024-12-11","2024-12-12","2024-12-13","2024-12-16","2024-12-17","2024-12-18","2024-12-19","2024-12-20","2024-12-23","2024-12-24","2024-12-25","2024-12-26","2024-12-27","2024-12-30","2024-12-31","2025-01-02","2025-01-03","2025-01-06","2025-01-07","2025-01-08","2025-01-09","2025-01-10","2025-01-13","2025-01-14","2025-01-15","2025-01-16","2025-01-17","2025-01-20","2025-01-21","2025-01-22","2025-01-23","2025-01-24","2025-01-27","2025-02-05","2025-02-06","2025-02-07","2025-02-10","2025-02-11","2025-02-12","2025-02-13","2025-02-14","2025-02-17","2025-02-18","2025-02-19","2025-02-20","2025-02-21","2025-02-24","2025-02-25","2025-02-26","2025-02-27","2025-02-28","2025-03-03","2025-03-04","2025-03-05","2025-03-06","2025-03-07","2025-03-10","2025-03-11","2025-03-12","2025-03-13","2025-03-14","2025-03-17","2025-03-18","2025-03-19","2025-03-20","2025-03-21","2025-03-24","2025-03-25","2025-03-26","2025-03-27","2025-03-28","2025-03-31","2025-04-01","2025-04-02","2025-04-03","2025-04-07","2025-04-08","2025-04-09","2025-04-10","2025-04-11","2025-04-14","2025-04-15","2025-04-16","2025-04-17","2025-04-18","2025-04-21","2025-04-22","2025-04-23","2025-04-24","2025-04-25","2025-04-28","2025-04-29","2025-04-30","2025-05-06","2025-05-07","2025-05-08","2025-05-09","2025-05-12","2025-05-13","2025-05-14","2025-05-15","2025-05-16","2025-05-19","2025-05-20","2025-05-21","2025-05-22","2025-05-23","2025-05-26","2025-05-27","2025-05-28","2025-05-29","2025-05-30","2025-06-03","2025-06-04","2025-06-05","2025-06-06","2025-06-09","2025-06-10","2025-06-11","2025-06-12","2025-06-13","2025-06-16","2025-06-17","2025-06-18","2025-06-19","2025-06-20","2025-06-23","2025-06-24","2025-06-25","2025-06-26","2025-06-27","2025-06-30","2025-07-01","2025-07-02","2025-07-03","2025-07-04","2025-07-07","2025-07-08","2025-07-09","2025-07-10","2025-07-11","2025-07-14","2025-07-15","2025-07-16","2025-07-17","2025-07-18","2025-07-21","2025-07-22","2025-07-23","2025-07-24","2025-07-25","2025-07-28","2025-07-29","2025-07-30","2025-07-31","2025-08-01","2025-08-04"],"y":[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,0.6238267180668515,7.463816477756762,3.7797524135854097,1.18234099647432,1.8546135459120343,0.280351384233299,1.5422711993731362,0.6426848010146031,3.2791806800006214,5.595522537817871,-1.9092395965515472,3.980323186347011,4.638128863855027,5.594144558867665,-0.14078233326728196,-2.4621236778800046,0.5915506167630795,4.0119149499576,5.064865720139977,2.7592131284658663,-1.8734065427218738,-0.5217386572810554,0.05451553690747346,2.194627378518163,1.9914769046976488,2.268725273897431,5.0023703277673945,4.597104689151465,-3.199224017868147,-0.8655556758973546,-3.712187828376024,1.908055656546665,-0.800351218372441,1.2008997802755914,-4.647623836099079,2.813750000863962,3.42658609111659,5.275545344710785,-3.4241892487022056,-0.8424665704728085,0.9214699753189421,0.7101035795325785,2.7664441275412344,0.053713364661316376,-1.5184236284824308,3.106598604961359,-3.1932726130386193,2.0456750583884578,0.7267495842996397,3.61402636420069,2.259898276570059,2.7182027036048915,-5.531988226111228,4.661507104075838,0.4791055435296556,4.860715921480207,2.8345307322528726,6.804949595633161,-2.8554689968463904,2.347727618480988,2.131743997487515,2.7956482861943464,6.6897318392621195,2.49008895237041,1.5914888473905755,-0.6329724730058786,3.5175187839732693,3.7159256489097707,1.4614961877748804,1.0797338167623387,-1.1858636044971256,4.202328146597175,1.8305542012485212,6.810217886346511,8.58136198200495,4.153606104300341,1.308518721582388,-3.3017993150962575,3.5146228712719676,-2.2067305972327844,1.7583900383408881,2.6065222986646504,0.0870249941451533,6.3043184861782535,1.9294097330072795,1.1447095088619825,-0.6532378455015149,-3.141915499524327,5.068520229147545,6.485832143456925,2.8818335308381684,2.793323235030307,-2.2897485834326226,0.9533509094703057,2.1717209314014205,4.525604406581696,3.9977378784676816,1.7042051398793348,1.3921561569030132,0.30928219773841903,5.943532099423905,0.7008398946210157,5.589251287866112,2.6342740452661673,1.9554142381248383,-4.539002309778343,1.231069352867685,-2.823232192074002,0.8468511344370597,1.3648419981019906,-4.212160421042068,5.881623915375325,3.8043056727088036,-3.6341636157046935,2.841932593302296,-1.1797762507914236,3.662524451880934,0.7483088061531973,4.278838328901035,3.511377455387713,3.170047093506824,1.141070523278735,1.467983927228147,-0.29850746248521887,3.429808760977636,4.128096440601283,6.250950194527697,-1.6912008952483646,5.573760458696448,2.21298224558803,-6.1205948386128775,6.42084230856846,3.4117949965743115,5.560005453508001,-2.1202229139390134,5.078062346392393,3.9712699263489206,2.1916236113743253,1.3526612703435426,-0.2313130471317777,1.797577786512953,9.18559554539491,2.8389090123802347,1.1626931176462278,3.0581692800629057,5.4311076644304634,5.7116001955409175,4.395488443446936,7.714502351956192,6.310108271808382,8.546813238787529,-1.491565989447139,-0.8812387691479202,-4.368012617807658,-1.6234656208099736,7.77850522424616,-3.8132492515891414,3.6341812474925277,-2.9347295868310326,0.18052082521476365,0.3153761198035596,5.830894228232786,0.771178394266844,0.9413339117468844,6.010246858249204,0.8581695463203687,2.4608577014658763,3.566503325155704,0.7735416981639318,4.071255812642622,-0.2090764997560015,-1.6038792167106983,2.876767728663417,5.364406742421757,5.684415851632107,1.0569011882827368,-1.1325149099940326,3.804918364314144,3.6728808906625368,1.8732651975433612,-1.5351039329447325,-4.406003837674222,1.8132267134033473,2.3089624657251466,2.6321985697531876,4.681087517842949,3.844886762619085,-0.3405746177488904,2.1751965552521924,-0.019257848973957437,7.581994023471687,2.5968679273694164,2.3444147562258593,-1.9992765295388892,3.704668512292713,-2.8254627510031405,1.5750583725880247,4.085890010486789,-4.871033448236144,2.713771217861659,3.1189523550755744,3.2109028717818404,-0.3725465641558827,-2.053612163897082,3.100191242762442,3.763936321997658,7.153052976135903,-2.051465328545671,1.1173506393921677,2.063881045974558,0.0925055489759199,5.616584908658755,3.1963343147144307,-2.942597103009886,2.5856431153466684,0.9848906977882184,-1.976235434312187,-1.7430431741876564,0.6517693857659559,1.0733061072554282,-2.348871430069611,3.2405628955146657],"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"价差=0 (IV=HV)","x":1,"xanchor":"right","xref":"x domain","y":0,"yanchor":"bottom","yref":"y"}],"height":400,"hovermode":"x unified","shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":0,"x1":1,"xref":"x domain","y0":0,"y1":0,"yref":"y"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"IV-HV价差图 (一年期)"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"价差 (%)"}}},                        {"responsive": true}                    )                };                            </script>        </div>
    </div>
    
    <!-- 波动率深度分析 -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">😊 波动率微笑曲线</div>
            <div>                            <div id="af6c8286-b769-46f3-9dc2-8526d40ab8e9" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("af6c8286-b769-46f3-9dc2-8526d40ab8e9")) {                    Plotly.newPlot(                        "af6c8286-b769-46f3-9dc2-8526d40ab8e9",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":3},"marker":{"size":4},"mode":"lines+markers","name":"IH 波动率微笑","x":[0.75,0.7708333333333334,0.7916666666666667,0.8125000000000001,0.8333333333333334,0.8541666666666667,0.8750000000000001,0.8958333333333333,0.9166666666666666,0.9375,0.9583333333333333,0.9791666666666666,1.0,1.0208333333333333,1.0416666666666667,1.0625,1.0833333333333335,1.1041666666666667,1.125,1.1458333333333335,1.1666666666666665,1.1875,1.2083333333333333,1.2291666666666665,1.25],"y":[16.861612938173177,16.10146413290084,15.511509565682639,16.26453154170387,16.7845142985955,16.420413776417785,15.389313229348863,16.257428705021333,15.892249637828543,15.486024078171996,15.943149614878378,14.901514629484804,15.664861744790784,15.147246617309843,15.23125515940286,14.09762940076357,14.348659623102874,14.299234493251522,13.595480292963172,13.302616635035353,13.25285666167623,13.350664783264524,12.695331062160701,13.208276390658263,12.449037724240727],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":3},"marker":{"size":4},"mode":"lines+markers","name":"IF 波动率微笑","x":[0.7500000000000001,0.7708333333333334,0.7916666666666667,0.8125000000000001,0.8333333333333334,0.8541666666666666,0.875,0.8958333333333334,0.9166666666666667,0.9375,0.9583333333333333,0.9791666666666666,1.0,1.0208333333333333,1.0416666666666667,1.0625,1.0833333333333335,1.1041666666666667,1.1249999999999998,1.1458333333333335,1.1666666666666665,1.1875,1.2083333333333333,1.2291666666666665,1.25],"y":[17.18293039323818,16.893844391700377,17.183048635432108,16.011026652826203,14.814715583485317,16.279965609525092,15.806216293168577,15.92048879693728,15.2305344133038,15.114367051172795,15.74139151945732,16.16526370123398,14.537932256973257,14.633918245230198,14.315408194427773,15.219447516237548,14.324997471104808,14.285369610442805,14.757426034535015,14.181506699848414,14.28061476079147,13.120876192017409,14.045275338106682,13.363702587710705,12.612579462831777],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003eMoneyness: %{x:.3f}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":3},"marker":{"size":4},"mode":"lines+markers","name":"IM 波动率微笑","x":[0.7500000000000001,0.7708333333333334,0.7916666666666666,0.8125000000000001,0.8333333333333334,0.8541666666666667,0.875,0.8958333333333334,0.9166666666666666,0.9375,0.9583333333333334,0.9791666666666667,1.0,1.0208333333333333,1.0416666666666665,1.0625,1.0833333333333333,1.1041666666666667,1.125,1.1458333333333333,1.1666666666666665,1.1874999999999998,1.2083333333333333,1.2291666666666665,1.25],"y":[21.14871448776784,20.592363900699567,20.054248740854376,19.745686616711414,19.793880014296196,19.06611611655581,18.30735586544627,19.715157232007826,18.451253007627024,18.230652866016648,19.239451566849983,18.975970276247242,18.85172122037716,18.29977304630455,18.17630979519227,18.18599046709674,17.292984784753834,16.92882454681022,17.178433631196267,16.726207079764535,16.21760879438406,16.25437183179856,16.09446207542393,16.06417670921355,15.41241851241245],"type":"scatter"}],                        {"annotations":[{"showarrow":false,"text":"ATM (平值)","x":1.0,"xanchor":"left","xref":"x","y":1,"yanchor":"top","yref":"y domain"},{"showarrow":false,"text":"OTM Put","x":0.9,"xanchor":"left","xref":"x","y":1,"yanchor":"top","yref":"y domain"},{"showarrow":false,"text":"OTM Call","x":1.1,"xanchor":"left","xref":"x","y":1,"yanchor":"top","yref":"y domain"}],"height":400,"legend":{"x":0,"y":1},"shapes":[{"line":{"color":"gray","dash":"dash"},"type":"line","x0":1.0,"x1":1.0,"xref":"x","y0":0,"y1":1,"yref":"y domain"},{"line":{"color":"red","dash":"dot"},"opacity":0.5,"type":"line","x0":0.9,"x1":0.9,"xref":"x","y0":0,"y1":1,"yref":"y domain"},{"line":{"color":"green","dash":"dot"},"opacity":0.5,"type":"line","x0":1.1,"x1":1.1,"xref":"x","y0":0,"y1":1,"yref":"y domain"}],"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率微笑曲线 (基于当前市场数据)"},"xaxis":{"range":[0.75,1.25],"title":{"text":"Moneyness (执行价\u002f标的价格)"}},"yaxis":{"title":{"text":"隐含波动率 (%)"}}},                        {"responsive": true}                    )                };                            </script>        </div>
        </div>
        <div class="chart-container">
            <div class="chart-title">📈 波动率期限结构</div>
            <div>                            <div id="c51cb781-b9ad-42d8-a804-550bd16a8313" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("c51cb781-b9ad-42d8-a804-550bd16a8313")) {                    Plotly.newPlot(                        "c51cb781-b9ad-42d8-a804-550bd16a8313",                        [{"hovertemplate":"\u003cb\u003eIH\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#1f77b4","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IH 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":[15.357084907392494,20.253603112928527,24.024442480673763,28.22165237607988,28.657584497790662,32.17902872743575,34.16014390940058,35.314818210328205],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIF\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#ff7f0e","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IF 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":[12.881889876129685,17.63503169233354,20.388220888455322,23.472269767876714,27.049701317652453,30.98744204487282,31.66528523387503,33.685242444248374],"type":"scatter"},{"hovertemplate":"\u003cb\u003eIM\u003c\u002fb\u003e\u003cbr\u003e期限: %{x}\u003cbr\u003e隐含波动率: %{y:.2f}%\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","line":{"color":"#2ca02c","width":3},"marker":{"size":8},"mode":"lines+markers","name":"IM 期限结构","x":["1W","2W","1M","2M","3M","6M","9M","1Y"],"y":[21.356423242073998,24.07822665436544,26.98198539222649,30.45927909241886,32.32159420708245,38.8702905813348,39.61675155019675,38.25059629167144],"type":"scatter"}],                        {"height":400,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率期限结构"},"xaxis":{"title":{"text":"期限"}},"yaxis":{"title":{"text":"隐含波动率 (%)"}}},                        {"responsive": true}                    )                };                            </script>        </div>
        </div>
    </div>
    
    <!-- 3D波动率曲面 -->
    <div class="chart-container full-width">
        <div class="chart-title">🌊 波动率曲面 (3D)</div>
        <div>                            <div id="e752772e-75ae-4fb4-80a6-5ef6fb33fbf8" class="plotly-graph-div" style="height:500px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("e752772e-75ae-4fb4-80a6-5ef6fb33fbf8")) {                    Plotly.newPlot(                        "e752772e-75ae-4fb4-80a6-5ef6fb33fbf8",                        [{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IH","scene":"scene","showscale":true,"x":[[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428571,0.8857142857142857,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714287,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2]],"y":[[7,7,7,7,7,7,7,7,7,7,7,7,7,7,7],[14,14,14,14,14,14,14,14,14,14,14,14,14,14,14],[30,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[60,60,60,60,60,60,60,60,60,60,60,60,60,60,60],[90,90,90,90,90,90,90,90,90,90,90,90,90,90,90],[180,180,180,180,180,180,180,180,180,180,180,180,180,180,180],[270,270,270,270,270,270,270,270,270,270,270,270,270,270,270],[365,365,365,365,365,365,365,365,365,365,365,365,365,365,365]],"z":[[18.638363687655175,16.200478522424877,17.170639257501204,16.784344884312606,19.513179916864143,16.930196933560826,18.728104271246476,21.743962332109763,20.257450191307218,16.877787184455013,17.10415743874359,18.76481540097824,14.590320055591588,16.293270397966573,19.10938553321327],[21.340672947474545,25.353449865586608,20.156496062449804,19.226234012233363,19.483533950200215,21.250033875384265,20.87580363274007,25.650019037429395,20.39923156802059,21.330841927719153,23.031132757910857,21.10395086197761,22.504595340922222,22.272990562686278,23.26370978506011],[22.584243659206845,26.909576378290218,24.927514343371044,25.504969415565952,24.300153116728307,28.350750520808475,20.914025003121075,27.798633972612347,23.650149022901736,28.09062333041641,27.51590131426204,27.160589278463355,26.860341597352466,29.826462967440794,24.21008865973314],[27.703196827742893,27.447825161452403,30.520115242166533,25.99802545919598,30.56153902497635,27.921508598490874,26.33847303739524,26.310534107687,26.880351642067023,25.526303800303445,29.567462821901948,30.299929876583825,30.58716440999965,27.50664535556617,29.4815979285508],[30.903472813211657,32.170002809646,31.58686739414824,30.804232753220596,26.84984503241543,30.192554416727376,30.722977668921576,29.103516155318566,29.962873576768708,27.927264204543476,29.92431993689047,31.635411172559436,34.875262218401545,28.7615204847604,30.845113302801252],[32.435859246063195,34.0675425192275,33.71541481652695,35.7551939152369,35.98050589067619,34.31708672276096,33.73797657123772,36.331734211373565,34.32654391317275,37.02433364263173,30.764202694639142,37.653351393029766,32.92742357701896,32.07987631144317,34.26929069013085],[34.05871804718459,37.41787942770995,35.26491680825331,36.69144534124411,34.10491485275963,35.57390343103372,36.18814968774761,35.609354188920136,39.17749046873311,37.19039760245516,36.466943058466285,32.40369373078191,36.94658343167509,38.752281068044844,35.00617927961104],[37.49679047517819,31.735897664776893,35.206329045431275,33.456033161472995,37.77521253994204,37.22568593268351,37.29003011868638,36.61959158164995,39.539935757438265,35.060546468164226,37.418720170757894,38.816890470002726,37.3387963679922,37.60578727507339,36.2433406017704]],"type":"surface"},{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IF","scene":"scene2","showscale":false,"x":[[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142857,0.9142857142857143,0.9428571428571428,0.9714285714285713,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.1142857142857143,1.1428571428571428,1.1714285714285715,1.2]],"y":[[7,7,7,7,7,7,7,7,7,7,7,7,7,7,7],[14,14,14,14,14,14,14,14,14,14,14,14,14,14,14],[30,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[60,60,60,60,60,60,60,60,60,60,60,60,60,60,60],[90,90,90,90,90,90,90,90,90,90,90,90,90,90,90],[180,180,180,180,180,180,180,180,180,180,180,180,180,180,180],[270,270,270,270,270,270,270,270,270,270,270,270,270,270,270],[365,365,365,365,365,365,365,365,365,365,365,365,365,365,365]],"z":[[16.622334402454044,20.65015603740984,20.419925526905715,14.64434459934656,14.860745866761082,14.326075982243545,21.449294485297017,17.898739789497395,17.627082709422442,18.86716017149958,15.870225836421767,17.493533150903307,15.560827157399764,19.22833322937393,16.22026273012182],[22.311011859323024,23.60439780298768,21.159077015593287,22.9712416857461,18.84314202389217,18.840945568384544,21.637206647134395,20.763461479516966,18.374464718261258,20.25599076990545,23.66629329228503,19.90830591965977,21.443744081855687,20.70092098117847,21.975577986803593],[26.098581863889724,26.476254315271426,26.244901372033784,26.78105702313582,25.92587640214625,25.423933112864177,23.9799389088695,18.85262236948047,24.21784512462101,23.441854693300336,23.667066364429008,21.655826890821313,26.70569814862731,21.565971262113102,26.237021968774776],[28.39506822847892,29.623827802196434,30.41040139623505,32.613592521517546,31.282747106053055,30.61558473729493,31.36457429642543,28.563382483680254,28.654558208686915,27.537875338502428,29.140530761745143,30.822307804142245,28.631187443036353,28.757998257021228,27.58867543565082],[34.08242817454897,30.82416496805786,31.81996041602894,30.22672004487114,31.49294593519404,26.887526542235157,30.767074347068128,29.258902167246447,33.11758371687459,32.077389929684976,30.006190036730107,29.175798262433002,28.810522998782712,31.910311480461544,29.5074761606258],[37.762316502693444,32.149229687022654,32.840575865200236,32.06761017056995,31.114505308353802,34.77255707404204,32.34607220950744,36.2741412122589,34.03048684482429,30.197593787200876,32.302364826678485,33.97501459841663,36.62491468674761,33.18557190092348,33.686848661273544],[33.27467527153986,38.717277942232194,31.674919026451864,36.55877080278807,35.94924897607809,36.581144943460295,37.19157751396936,38.040471610320395,36.008170094702244,32.51215648855768,39.15805302742387,34.82598149374543,37.94888084234429,35.64323669822251,37.840838700099596],[37.76233650604349,36.88527282180185,39.40464210884724,32.552747585568824,36.79200223554029,37.48400295302163,38.13410737431606,36.233868751647776,33.54230011961853,38.07952235964895,35.094081834098176,38.770781380412465,38.3345529297351,37.629938034679775,40.84788619663532]],"type":"surface"},{"colorscale":[[0.0,"#440154"],[0.1111111111111111,"#482878"],[0.2222222222222222,"#3e4989"],[0.3333333333333333,"#31688e"],[0.4444444444444444,"#26828e"],[0.5555555555555556,"#1f9e89"],[0.6666666666666666,"#35b779"],[0.7777777777777778,"#6ece58"],[0.8888888888888888,"#b5de2b"],[1.0,"#fde725"]],"name":"IM","scene":"scene3","showscale":false,"x":[[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2],[0.8,0.8285714285714286,0.8571428571428572,0.8857142857142858,0.9142857142857144,0.942857142857143,0.9714285714285714,1.0,1.0285714285714285,1.0571428571428572,1.0857142857142856,1.114285714285714,1.1428571428571428,1.1714285714285715,1.2]],"y":[[7,7,7,7,7,7,7,7,7,7,7,7,7,7,7],[14,14,14,14,14,14,14,14,14,14,14,14,14,14,14],[30,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[60,60,60,60,60,60,60,60,60,60,60,60,60,60,60],[90,90,90,90,90,90,90,90,90,90,90,90,90,90,90],[180,180,180,180,180,180,180,180,180,180,180,180,180,180,180],[270,270,270,270,270,270,270,270,270,270,270,270,270,270,270],[365,365,365,365,365,365,365,365,365,365,365,365,365,365,365]],"z":[[18.17631338266491,18.5380847908315,17.13735438658327,17.445574271787226,15.253767915282088,12.562459637676252,18.311088925973003,15.976954549341334,18.519872415394474,19.6264279859601,17.76566380960892,18.37336801437041,14.981016987438366,19.621294580138706,14.62205906362275],[20.599195876916742,19.465975672139695,21.443869744167383,21.07689827530085,18.16902276368059,20.008064853910717,18.40983766035973,19.93390875064261,21.8627273137836,18.13921911729685,24.603368768263813,20.320820254040843,22.739639301550703,25.979356437149676,18.280774237667032],[24.451205967653454,24.690047069219094,23.342028878478054,27.352584364284706,24.69410347086283,27.58947224321134,23.900009027393317,25.708605479985273,24.127610920638208,24.183755406648388,25.529632637458,26.288700578812662,23.49570492625176,23.61119688391547,24.08859177560541],[28.571970499699884,25.93566245191844,28.128703886342386,30.854954756691182,25.858689147402465,27.999796907507662,32.00088359319677,27.281817049121372,26.658275805671106,29.041988198798023,28.530081949413727,30.304562020255236,25.489807767598332,28.87171142764229,29.077461035831742],[30.711386288047727,29.359186882308435,31.10457386379729,33.02973736308256,30.735156968142434,30.853582351249226,31.248727808593053,30.039247742780145,29.158675899471586,31.153934263415074,33.53536705728434,31.92800200422616,30.23199634082747,33.15643136748035,32.73495606737903],[36.79179892349342,30.486149286746322,34.60546293346643,37.72260010022826,37.51803468404865,33.870494832506644,32.78786491522739,32.31473816083387,33.86252429724334,33.82097547018151,34.89437239066047,33.69605047209189,32.841696154120754,33.75968160812657,33.93971173862345],[35.37370086741607,33.40275804621069,35.78218252750089,34.981866357145854,35.36203440249723,33.958399482239756,37.16597866563955,36.7297823677749,31.17999521654566,37.946742251391754,36.25260046271156,36.97426718529479,37.38487181630343,37.740173181083385,34.003201254380876],[40.922654882245844,36.392088737538565,37.04510275269811,39.19044177674401,34.19271977599286,40.35897454180935,36.24335370188507,36.46110712259465,42.39484966155184,34.36175162638736,33.95569747759151,38.38011070481683,35.65040913161868,35.87142445197631,37.49135855829587]],"type":"surface"}],                        {"annotations":[{"font":{"size":16},"showarrow":false,"text":"IH 波动率曲面","x":0.14444444444444446,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IF 波动率曲面","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"IM 波动率曲面","x":0.8555555555555556,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"}],"height":500,"scene":{"domain":{"x":[0.0,0.2888888888888889],"y":[0.0,1.0]},"xaxis":{"title":{"text":"Moneyness"}},"yaxis":{"title":{"text":"期限 (天)"}},"zaxis":{"title":{"text":"隐含波动率 (%)"}}},"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"text":"波动率曲面 (3D)"},"scene2":{"domain":{"x":[0.35555555555555557,0.6444444444444445],"y":[0.0,1.0]}},"scene3":{"domain":{"x":[0.7111111111111111,1.0],"y":[0.0,1.0]}}},                        {"responsive": true}                    )                };                            </script>        </div>
    </div>
    
    <!-- 期权分析文字 -->
    <div class="content-section">
        <div class="text-content">
            <p>
<h2>🎯 期权市场分析</p>
<h3>📊 PCR (Put/Call Ratio) 解读
PCR指标是重要的市场情绪指标：</li>
<li><strong>PCR > 1.3</strong>: 市场偏悲观，看跌期权交易活跃</li>
<li><strong>PCR < 0.7</strong>: 市场偏乐观，看涨期权交易活跃</li>
<li><strong>PCR ≈ 1.0</strong>: 市场情绪相对平衡</p>
<h3>🥧 持仓结构分析
期权持仓分布反映投资者策略偏好：</li>
<li><strong>看涨期权</strong>: 直接做多策略</li>
<li><strong>看跌期权</strong>: 对冲或做空策略</li>
<li><strong>跨式组合</strong>: 波动率交易策略</li>
<li><strong>宽跨式组合</strong>: 大幅波动预期策略</p>
<h3>💡 交易策略建议
基于当前市场状况：
1. **波动率交易</strong>: 关注IV-HV价差机会
2. **方向性交易</strong>: 结合技术分析确定趋势
3. **套利交易</strong>: 利用期限结构和微笑曲线异常
4. **风险管理</strong>: 动态调整持仓和对冲比例
</p>
        </div>
    </div>
    
    <!-- 期权持仓分布 -->
    <div class="chart-container full-width">
        <div class="chart-title">🥧 期权持仓分布图</div>
        <div>                            <div id="1cd2471d-d0e5-4bc0-a9ef-75fd1e1c737b" class="plotly-graph-div" style="height:400px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("1cd2471d-d0e5-4bc0-a9ef-75fd1e1c737b")) {                    Plotly.newPlot(                        "1cd2471d-d0e5-4bc0-a9ef-75fd1e1c737b",                        [{"domain":{"x":[0.0,0.2888888888888889],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#2E8B57","#DC143C","#4169E1","#FF8C00","#9370DB"]},"name":"IH持仓","showlegend":true,"textfont":{"size":10},"textinfo":"label+percent","values":[35.2,42.8,8.5,6.3,7.2],"type":"pie"},{"domain":{"x":[0.35555555555555557,0.6444444444444445],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#2E8B57","#DC143C","#4169E1","#FF8C00","#9370DB"]},"name":"IF持仓","showlegend":false,"textfont":{"size":10},"textinfo":"label+percent","values":[38.5,41.2,9.1,5.8,5.4],"type":"pie"},{"domain":{"x":[0.7111111111111111,1.0],"y":[0.0,1.0]},"hovertemplate":"\u003cb\u003e%{label}\u003c\u002fb\u003e\u003cbr\u003e占比: %{percent}\u003cbr\u003e持仓量: %{value:.1f}万手\u003cbr\u003e\u003cextra\u003e\u003c\u002fextra\u003e","labels":["看涨期权","看跌期权","跨式组合","宽跨式组合","其他策略"],"marker":{"colors":["#2E8B57","#DC143C","#4169E1","#FF8C00","#9370DB"]},"name":"IM持仓","showlegend":false,"textfont":{"size":10},"textinfo":"label+percent","values":[42.3,39.7,7.8,4.9,5.3],"type":"pie"}],                        {"annotations":[{"font":{"color":"gray","size":10},"showarrow":false,"text":"数据来源: 中金所期权持仓统计","x":0.5,"xanchor":"center","xref":"paper","y":-0.1,"yanchor":"bottom","yref":"paper"},{"font":{"color":"gray","size":10},"showarrow":false,"text":"数据来源: 中金所期权持仓统计","x":0.5,"xanchor":"center","xref":"paper","y":-0.1,"yanchor":"bottom","yref":"paper"},{"font":{"color":"gray","size":10},"showarrow":false,"text":"数据来源: 中金所期权持仓统计","x":0.5,"xanchor":"center","xref":"paper","y":-0.1,"yanchor":"bottom","yref":"paper"}],"height":400,"template":{"data":{"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"#E5ECF6","showlakes":true,"showland":true,"subunitcolor":"white"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","gridwidth":2,"linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"bgcolor":"#E5ECF6","caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","zerolinewidth":2}}},"title":{"font":{"color":"#333","size":16},"text":"期权持仓分布图 (基于最新市场数据)","x":0.5,"xanchor":"center"}},                        {"responsive": true}                    )                };                            </script>        </div>
    </div>
    
    <div class="footer">
        <p>📊 中国股指期货报告自动生成系统 | 生成时间: 2025-08-04 17:48:17</p>
        <p>💡 本报告基于一年期历史数据生成，包含IH、IF、IM三大股指期货合约的全面分析</p>
        <p>⚠️ 本报告仅供参考，投资有风险，入市需谨慎</p>
    </div>
</body>
</html>
