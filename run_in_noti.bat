@echo off
echo 🚀 在noti环境中运行股指期货报告系统
echo ================================================

REM 激活conda环境并运行程序
call conda activate noti

echo.
echo 选择要运行的程序:
echo 1. 运行演示 (run_demo.py)
echo 2. 运行完整程序 (main.py) 
echo 3. 查看图表 (view_charts.py)
echo 4. 生成快速报告 (quick_report.py)
echo 5. 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo 🎯 运行演示模式...
    python run_demo.py
) else if "%choice%"=="2" (
    echo 🎯 运行完整程序...
    python main.py
) else if "%choice%"=="3" (
    echo 🎯 查看图表...
    python view_charts.py
) else if "%choice%"=="4" (
    echo 🎯 生成快速报告...
    python quick_report.py
) else if "%choice%"=="5" (
    echo 👋 退出程序
    exit /b 0
) else (
    echo ❌ 无效选择，请重新运行
)

echo.
echo 程序执行完成！
pause
