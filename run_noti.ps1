# 在noti环境中运行股指期货报告系统
Write-Host "🚀 在noti环境中运行股指期货报告系统" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# 激活conda环境的函数
function Invoke-InNotiEnv {
    param([string]$Command)
    
    $fullCommand = "conda activate noti; $Command"
    powershell -Command "& { $fullCommand }"
}

# 显示菜单
function Show-Menu {
    Write-Host ""
    Write-Host "选择要运行的程序:" -ForegroundColor Yellow
    Write-Host "1. 运行演示 (run_demo.py)" -ForegroundColor Cyan
    Write-Host "2. 运行完整程序 (main.py)" -ForegroundColor Cyan  
    Write-Host "3. 查看图表 (view_charts.py)" -ForegroundColor Cyan
    Write-Host "4. 生成快速报告 (quick_report.py)" -ForegroundColor Cyan
    Write-Host "5. 检查环境状态" -ForegroundColor Cyan
    Write-Host "6. 退出" -ForegroundColor Red
    Write-Host ""
}

# 主循环
do {
    Show-Menu
    $choice = Read-Host "请输入选择 (1-6)"
    
    switch ($choice) {
        "1" {
            Write-Host "🎯 运行演示模式..." -ForegroundColor Green
            Invoke-InNotiEnv "python run_demo.py"
        }
        "2" {
            Write-Host "🎯 运行完整程序..." -ForegroundColor Green
            Invoke-InNotiEnv "python main.py"
        }
        "3" {
            Write-Host "🎯 查看图表..." -ForegroundColor Green
            Invoke-InNotiEnv "python view_charts.py"
        }
        "4" {
            Write-Host "🎯 生成快速报告..." -ForegroundColor Green
            Invoke-InNotiEnv "python quick_report.py"
        }
        "5" {
            Write-Host "🔍 检查环境状态..." -ForegroundColor Green
            Write-Host "Python版本:" -ForegroundColor Yellow
            Invoke-InNotiEnv "python --version"
            Write-Host "已安装的关键包:" -ForegroundColor Yellow
            Invoke-InNotiEnv "python -c 'import pandas, numpy, plotly, loguru; print(\"✅ 核心依赖包正常\")'"
        }
        "6" {
            Write-Host "👋 退出程序" -ForegroundColor Red
            break
        }
        default {
            Write-Host "❌ 无效选择，请重新输入" -ForegroundColor Red
        }
    }
    
    if ($choice -ne "6") {
        Write-Host ""
        Write-Host "程序执行完成！" -ForegroundColor Green
        Read-Host "按Enter键继续..."
    }
    
} while ($choice -ne "6")

Write-Host "感谢使用！" -ForegroundColor Green
