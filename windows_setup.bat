@echo off
REM 中国股指期货报告系统 - Windows环境自动配置脚本
REM 版本: v1.0
REM 创建时间: 2025-08-03

echo ========================================
echo 中国股指期货报告系统 - Windows环境配置
echo ========================================
echo.

REM 检查Python是否安装
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo ✅ Python已安装
    python --version
)
echo.

REM 检查pip
echo [2/6] 检查pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip未安装
    pause
    exit /b 1
) else (
    echo ✅ pip已安装
)
echo.

REM 创建虚拟环境
echo [3/6] 创建Python虚拟环境...
if exist "venv" (
    echo ⚠️  虚拟环境已存在，跳过创建
) else (
    echo 正在创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    ) else (
        echo ✅ 虚拟环境创建成功
    )
)
echo.

REM 激活虚拟环境并安装依赖
echo [4/6] 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 虚拟环境激活失败
    pause
    exit /b 1
)

echo 正在安装Python依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
) else (
    echo ✅ 依赖安装成功
)
echo.

REM 检查关键依赖
echo [5/6] 验证关键依赖...
python -c "import pandas; print('✅ pandas:', pandas.__version__)" 2>nul || echo "❌ pandas导入失败"
python -c "import plotly; print('✅ plotly:', plotly.__version__)" 2>nul || echo "❌ plotly导入失败"
python -c "import selenium; print('✅ selenium:', selenium.__version__)" 2>nul || echo "❌ selenium导入失败"
echo.

REM 创建配置文件
echo [6/6] 配置项目...
if not exist "config\config.py" (
    if exist "config\config_template.py" (
        echo 正在创建配置文件...
        copy "config\config_template.py" "config\config.py" >nul
        echo ✅ 配置文件已创建，请编辑 config\config.py
    ) else (
        echo ⚠️  配置模板不存在，请手动创建配置文件
    )
) else (
    echo ✅ 配置文件已存在
)
echo.

REM 创建必要目录
echo 创建输出目录...
if not exist "output" mkdir output
if not exist "output\reports" mkdir output\reports
if not exist "output\images" mkdir output\images
if not exist "output\articles" mkdir output\articles
if not exist "output\temp" mkdir output\temp
if not exist "logs" mkdir logs
echo ✅ 目录结构已创建
echo.

echo ========================================
echo 🎉 Windows环境配置完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 安装Wind终端并登录
echo 2. 安装Wind API: pip install WindPy-xxx.whl
echo 3. 编辑配置文件: config\config.py
echo 4. 运行测试: python run_demo.py
echo.
echo 激活虚拟环境命令: venv\Scripts\activate
echo 退出虚拟环境命令: deactivate
echo.
pause
